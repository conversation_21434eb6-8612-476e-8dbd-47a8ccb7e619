"""
POE Currency Data Collector

This module provides automated data collection functionality to build
historical price data for analysis.
"""

import schedule
import time
import logging
from datetime import datetime
from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('poe_data_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class DataCollector:
    """Automated data collection for POE currency prices"""
    
    def __init__(self, league: str = "Standard", rate_limit_delay: float = 1.0):
        """
        Initialize the data collector
        
        Args:
            league: POE league to collect data for
            rate_limit_delay: Delay between API calls
        """
        self.api = POENinjaAPI(league=league, rate_limit_delay=rate_limit_delay)
        self.data_manager = CurrencyDataManager()
        self.league = league
        
    def collect_data(self):
        """Collect current currency data and save snapshot"""
        try:
            logger.info(f"Starting data collection for league: {self.league}")
            
            # Fetch currency data
            currency_data = self.api.get_currency_data('currency')
            fragment_data = self.api.get_currency_data('fragment')
            
            if not currency_data:
                logger.error("Failed to fetch currency data")
                return False
                
            # Prepare data for saving
            all_data = {'currency': currency_data}
            if fragment_data:
                all_data['fragment'] = fragment_data
                
            # Save snapshot
            snapshot_path = self.data_manager.save_currency_snapshot(all_data)
            
            # Log summary
            currency_count = len(currency_data.get('lines', []))
            fragment_count = len(fragment_data.get('lines', [])) if fragment_data else 0
            
            logger.info(f"✓ Collected {currency_count} currencies and {fragment_count} fragments")
            logger.info(f"✓ Saved to: {snapshot_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error during data collection: {e}")
            return False
    
    def start_scheduled_collection(self, interval_hours: int = 1):
        """
        Start scheduled data collection
        
        Args:
            interval_hours: Hours between data collection runs
        """
        logger.info(f"Starting scheduled data collection every {interval_hours} hour(s)")
        
        # Schedule the job
        schedule.every(interval_hours).hours.do(self.collect_data)
        
        # Run initial collection
        self.collect_data()
        
        # Keep the scheduler running
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Data collection stopped by user")
        except Exception as e:
            logger.error(f"Scheduler error: {e}")
    
    def collect_once(self):
        """Collect data once and exit"""
        success = self.collect_data()
        if success:
            logger.info("Data collection completed successfully")
        else:
            logger.error("Data collection failed")
        return success


def main():
    """Main function for the data collector"""
    import argparse
    
    parser = argparse.ArgumentParser(description='POE Currency Data Collector')
    parser.add_argument('--league', default='Standard', help='POE league to collect data for')
    parser.add_argument('--interval', type=int, default=1, help='Collection interval in hours')
    parser.add_argument('--once', action='store_true', help='Collect data once and exit')
    
    args = parser.parse_args()
    
    collector = DataCollector(league=args.league)
    
    if args.once:
        collector.collect_once()
    else:
        collector.start_scheduled_collection(args.interval)


if __name__ == "__main__":
    main()
