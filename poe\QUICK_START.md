# 🚀 POE Currency Analysis - Quick Start

## Getting Started in 3 Steps

### 1. Start the Web App
**Windows:** Double-click `start_poe_app.bat`
**Command Line:** `python run_poe_app.py`

### 2. Open Your Browser
Go to: http://localhost:5006

### 3. Collect Data
Click the "Collect Data" button in the web interface

## What You'll See

- **Dashboard**: Current currency prices and market overview
- **Analysis**: Detailed price trends and trading opportunities  
- **Charts**: Interactive visualizations of price movements
- **Data Collection**: Manage your historical price data

## Automated Data Collection

For continuous monitoring, run:
```bash
python poe_data_collector.py --interval 1
```
This collects data every hour automatically.

## Files Overview

- `poe_app.py` - Main web application
- `poe_get_currency_prices.py` - API client for poe.ninja
- `poe_currency_analyzer.py` - Analysis and visualization engine
- `poe_data_collector.py` - Automated data collection
- `poe_demo.py` - Command-line demo of all features

## Need Help?

Check `POE_CURRENCY_README.md` for detailed documentation.

---
**Happy Trading, Exile!** 🗡️
