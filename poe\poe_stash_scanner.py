"""
POE Stash Tab Scanner Module

This module scans stash tabs and extracts valuable item data
for price analysis and sell recommendations.
"""

import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


@dataclass
class StashItem:
    """Represents an item found in stash"""
    name: str
    base_type: str
    item_level: int
    rarity: str  # Normal, Magic, Rare, Unique
    category: str  # Currency, Equipment, Gem, etc.
    stack_size: int
    position: Tuple[int, int]  # x, y coordinates
    properties: Dict
    requirements: Dict
    sockets: List[Dict]
    influences: List[str]
    synthesised: bool
    fractured: bool
    corrupted: bool
    identified: bool
    note: str  # Player note/price
    raw_data: Dict  # Full item data from API
    
    def __post_init__(self):
        """Calculate derived properties"""
        self.is_currency = self.category == "Currency"
        self.is_equipment = self.category in ["Weapons", "Armour", "Accessories"]
        self.is_gem = self.category == "Gems"
        self.is_valuable = self._is_potentially_valuable()
    
    def _is_potentially_valuable(self) -> bool:
        """Determine if item might be valuable"""
        # Currency is always valuable
        if self.is_currency:
            return True
            
        # Unique items are often valuable
        if self.rarity == "Unique":
            return True
            
        # High level items
        if self.item_level >= 80:
            return True
            
        # Items with influences
        if self.influences:
            return True
            
        # Synthesised or fractured items
        if self.synthesised or self.fractured:
            return True
            
        # 6-link items
        if self._has_six_links():
            return True
            
        # Items with player notes (usually priced)
        if self.note:
            return True
            
        return False
    
    def _has_six_links(self) -> bool:
        """Check if item has 6 links"""
        if not self.sockets:
            return False
            
        # Count connected sockets
        max_group = 0
        for socket in self.sockets:
            group = socket.get('group', 0)
            max_group = max(max_group, group)
            
        # If all sockets are in same group and there are 6, it's 6-linked
        return len(self.sockets) == 6 and max_group == 0


class StashScanner:
    """Scanner for analyzing stash tab contents"""
    
    # Item categories mapping
    ITEM_CATEGORIES = {
        'Currency': ['Currency'],
        'Weapons': ['One Hand Maces', 'Two Hand Maces', 'One Hand Axes', 'Two Hand Axes', 
                   'One Hand Swords', 'Two Hand Swords', 'Thrusting One Hand Swords',
                   'Bows', 'Staves', 'Warstaves', 'Daggers', 'Claws', 'Sceptres', 'Wands'],
        'Armour': ['Gloves', 'Boots', 'Body Armours', 'Helmets', 'Shields'],
        'Accessories': ['Rings', 'Amulets', 'Belts'],
        'Gems': ['Active Skill Gems', 'Support Skill Gems'],
        'Flasks': ['Life Flasks', 'Mana Flasks', 'Hybrid Flasks', 'Utility Flasks'],
        'Maps': ['Maps'],
        'Fragments': ['Map Fragments'],
        'Divination': ['Divination Cards'],
        'Essences': ['Essences'],
        'Fossils': ['Fossils'],
        'Resonators': ['Resonators'],
        'Incubators': ['Incubators'],
        'Scarabs': ['Scarabs'],
        'Prophecies': ['Prophecies'],
        'Leaguestones': ['Leaguestones']
    }
    
    def __init__(self):
        """Initialize stash scanner"""
        self.category_map = self._build_category_map()
    
    def _build_category_map(self) -> Dict[str, str]:
        """Build reverse mapping from item class to category"""
        category_map = {}
        for category, item_classes in self.ITEM_CATEGORIES.items():
            for item_class in item_classes:
                category_map[item_class] = category
        return category_map
    
    def scan_stash_tab(self, stash_data: Dict) -> List[StashItem]:
        """
        Scan a stash tab and extract item information
        
        Args:
            stash_data: Raw stash tab data from POE API
            
        Returns:
            List of StashItem objects
        """
        items = []
        
        if 'items' not in stash_data:
            return items
            
        for item_data in stash_data['items']:
            try:
                item = self._parse_item(item_data)
                if item:
                    items.append(item)
            except Exception as e:
                logger.error(f"Error parsing item: {e}")
                continue
                
        return items
    
    def _parse_item(self, item_data: Dict) -> Optional[StashItem]:
        """Parse individual item data"""
        try:
            # Basic item information
            name = item_data.get('name', '')
            base_type = item_data.get('typeLine', '')
            item_level = item_data.get('ilvl', 0)
            
            # Handle item naming (some items have empty name)
            if not name:
                name = base_type
            elif name != base_type:
                name = f"{name} {base_type}"
            
            # Rarity and category
            frame_type = item_data.get('frameType', 0)
            rarity = self._get_rarity_from_frame(frame_type)
            
            # Determine category
            item_class = item_data.get('category', {}).get('class', '')
            category = self.category_map.get(item_class, 'Other')
            
            # Stack size
            stack_size = item_data.get('stackSize', 1)
            
            # Position
            x = item_data.get('x', 0)
            y = item_data.get('y', 0)
            position = (x, y)
            
            # Properties and requirements
            properties = self._parse_properties(item_data.get('properties', []))
            requirements = self._parse_requirements(item_data.get('requirements', []))
            
            # Sockets
            sockets = item_data.get('sockets', [])
            
            # Special properties
            influences = self._parse_influences(item_data)
            synthesised = item_data.get('synthesised', False)
            fractured = item_data.get('fractured', False)
            corrupted = item_data.get('corrupted', False)
            identified = item_data.get('identified', True)
            
            # Player note
            note = item_data.get('note', '')
            
            return StashItem(
                name=name,
                base_type=base_type,
                item_level=item_level,
                rarity=rarity,
                category=category,
                stack_size=stack_size,
                position=position,
                properties=properties,
                requirements=requirements,
                sockets=sockets,
                influences=influences,
                synthesised=synthesised,
                fractured=fractured,
                corrupted=corrupted,
                identified=identified,
                note=note,
                raw_data=item_data
            )
            
        except Exception as e:
            logger.error(f"Error parsing item data: {e}")
            return None
    
    def _get_rarity_from_frame(self, frame_type: int) -> str:
        """Convert frame type to rarity string"""
        frame_map = {
            0: "Normal",
            1: "Magic", 
            2: "Rare",
            3: "Unique",
            4: "Gem",
            5: "Currency",
            6: "Divination Card",
            7: "Quest Item",
            8: "Prophecy",
            9: "Relic"
        }
        return frame_map.get(frame_type, "Unknown")
    
    def _parse_properties(self, properties: List[Dict]) -> Dict:
        """Parse item properties"""
        parsed = {}
        for prop in properties:
            name = prop.get('name', '')
            values = prop.get('values', [])
            
            if values:
                # Extract numeric values where possible
                value_str = ' '.join([str(v[0]) for v in values])
                parsed[name] = value_str
            else:
                parsed[name] = True
                
        return parsed
    
    def _parse_requirements(self, requirements: List[Dict]) -> Dict:
        """Parse item requirements"""
        parsed = {}
        for req in requirements:
            name = req.get('name', '')
            values = req.get('values', [])
            
            if values:
                value_str = ' '.join([str(v[0]) for v in values])
                parsed[name] = value_str
                
        return parsed
    
    def _parse_influences(self, item_data: Dict) -> List[str]:
        """Parse item influences"""
        influences = []
        
        influence_keys = [
            'shaper', 'elder', 'crusader', 'redeemer', 'hunter', 'warlord'
        ]
        
        for influence in influence_keys:
            if item_data.get(influence, False):
                influences.append(influence.title())
                
        return influences
    
    def filter_valuable_items(self, items: List[StashItem], min_value_threshold: float = 1.0) -> List[StashItem]:
        """
        Filter items that are likely to be valuable
        
        Args:
            items: List of stash items
            min_value_threshold: Minimum estimated value in chaos orbs
            
        Returns:
            Filtered list of valuable items
        """
        valuable_items = []
        
        for item in items:
            if item.is_valuable:
                valuable_items.append(item)
                
        return valuable_items
    
    def categorize_items(self, items: List[StashItem]) -> Dict[str, List[StashItem]]:
        """
        Categorize items by type
        
        Args:
            items: List of stash items
            
        Returns:
            Dictionary with items grouped by category
        """
        categorized = {}
        
        for item in items:
            category = item.category
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(item)
            
        return categorized
    
    def get_stash_summary(self, items: List[StashItem]) -> Dict:
        """
        Get summary statistics for stash contents
        
        Args:
            items: List of stash items
            
        Returns:
            Summary statistics
        """
        total_items = len(items)
        valuable_items = len([item for item in items if item.is_valuable])
        
        # Count by category
        categories = self.categorize_items(items)
        category_counts = {cat: len(items) for cat, items in categories.items()}
        
        # Count by rarity
        rarity_counts = {}
        for item in items:
            rarity = item.rarity
            rarity_counts[rarity] = rarity_counts.get(rarity, 0) + 1
        
        return {
            'total_items': total_items,
            'valuable_items': valuable_items,
            'category_counts': category_counts,
            'rarity_counts': rarity_counts,
            'scan_timestamp': datetime.now().isoformat()
        }
