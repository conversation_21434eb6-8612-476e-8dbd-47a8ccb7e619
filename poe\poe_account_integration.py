"""
POE Account Integration Module

This module handles OAuth authentication with the Path of Exile API
to access player stash tabs and character data for price analysis.
"""

import requests
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class POEAccount:
    """Represents a POE account with authentication details"""
    account_name: str
    access_token: str
    refresh_token: str
    token_expires: datetime
    realm: str = "pc"  # pc, xbox, sony
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for storage"""
        return {
            'account_name': self.account_name,
            'access_token': self.access_token,
            'refresh_token': self.refresh_token,
            'token_expires': self.token_expires.isoformat(),
            'realm': self.realm
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'POEAccount':
        """Create from dictionary"""
        return cls(
            account_name=data['account_name'],
            access_token=data['access_token'],
            refresh_token=data['refresh_token'],
            token_expires=datetime.fromisoformat(data['token_expires']),
            realm=data.get('realm', 'pc')
        )


class POEAccountAPI:
    """Client for POE Account API integration"""
    
    # POE OAuth endpoints
    OAUTH_BASE_URL = "https://www.pathofexile.com/oauth"
    API_BASE_URL = "https://api.pathofexile.com"
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str = "http://localhost:5006/auth/callback"):
        """
        Initialize POE Account API client
        
        Args:
            client_id: OAuth client ID from POE developer portal
            client_secret: OAuth client secret
            redirect_uri: Redirect URI for OAuth flow
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri
        self.account_file = "poe_data/poe_account.json"
        
    def get_authorization_url(self, state: str = None) -> str:
        """
        Get OAuth authorization URL for user to authenticate
        
        Args:
            state: Optional state parameter for security
            
        Returns:
            Authorization URL
        """
        params = {
            'client_id': self.client_id,
            'response_type': 'code',
            'scope': 'account:stashes account:characters',
            'redirect_uri': self.redirect_uri
        }
        
        if state:
            params['state'] = state
            
        query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        return f"{self.OAUTH_BASE_URL}/authorize?{query_string}"
    
    def exchange_code_for_token(self, authorization_code: str) -> Optional[POEAccount]:
        """
        Exchange authorization code for access token
        
        Args:
            authorization_code: Code received from OAuth callback
            
        Returns:
            POEAccount object if successful
        """
        try:
            token_data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'authorization_code',
                'code': authorization_code,
                'redirect_uri': self.redirect_uri
            }
            
            response = requests.post(f"{self.OAUTH_BASE_URL}/token", data=token_data)
            response.raise_for_status()
            
            token_info = response.json()
            
            # Get account info
            account_info = self._get_account_info(token_info['access_token'])
            
            if account_info:
                account = POEAccount(
                    account_name=account_info['name'],
                    access_token=token_info['access_token'],
                    refresh_token=token_info['refresh_token'],
                    token_expires=datetime.now() + timedelta(seconds=token_info['expires_in'])
                )
                
                # Save account info
                self._save_account(account)
                return account
                
        except Exception as e:
            logger.error(f"Error exchanging code for token: {e}")
            
        return None
    
    def _get_account_info(self, access_token: str) -> Optional[Dict]:
        """Get account information using access token"""
        try:
            headers = {'Authorization': f'Bearer {access_token}'}
            response = requests.get(f"{self.API_BASE_URL}/profile", headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None
    
    def load_account(self) -> Optional[POEAccount]:
        """Load saved account from file"""
        try:
            if os.path.exists(self.account_file):
                with open(self.account_file, 'r') as f:
                    data = json.load(f)
                    account = POEAccount.from_dict(data)
                    
                    # Check if token needs refresh
                    if datetime.now() >= account.token_expires:
                        account = self._refresh_token(account)
                        
                    return account
        except Exception as e:
            logger.error(f"Error loading account: {e}")
            
        return None
    
    def _save_account(self, account: POEAccount) -> bool:
        """Save account to file"""
        try:
            os.makedirs(os.path.dirname(self.account_file), exist_ok=True)
            with open(self.account_file, 'w') as f:
                json.dump(account.to_dict(), f, indent=2)
            return True
        except Exception as e:
            logger.error(f"Error saving account: {e}")
            return False
    
    def _refresh_token(self, account: POEAccount) -> Optional[POEAccount]:
        """Refresh expired access token"""
        try:
            refresh_data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'refresh_token',
                'refresh_token': account.refresh_token
            }
            
            response = requests.post(f"{self.OAUTH_BASE_URL}/token", data=refresh_data)
            response.raise_for_status()
            
            token_info = response.json()
            
            # Update account with new tokens
            account.access_token = token_info['access_token']
            account.refresh_token = token_info['refresh_token']
            account.token_expires = datetime.now() + timedelta(seconds=token_info['expires_in'])
            
            # Save updated account
            self._save_account(account)
            return account
            
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return None
    
    def get_stash_tabs(self, account: POEAccount, league: str = None) -> Optional[List[Dict]]:
        """
        Get list of stash tabs for account
        
        Args:
            account: POE account with valid token
            league: League name (if None, uses current league)
            
        Returns:
            List of stash tab information
        """
        try:
            headers = {'Authorization': f'Bearer {account.access_token}'}
            
            # If no league specified, get current league from account
            if not league:
                league = self._get_current_league(account)
                
            if not league:
                logger.error("No league specified and couldn't determine current league")
                return None
                
            url = f"{self.API_BASE_URL}/stash/{account.account_name}"
            params = {'league': league}
            
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            return data.get('stashes', [])
            
        except Exception as e:
            logger.error(f"Error getting stash tabs: {e}")
            return None
    
    def get_stash_tab_contents(self, account: POEAccount, stash_id: str, league: str) -> Optional[Dict]:
        """
        Get contents of a specific stash tab
        
        Args:
            account: POE account with valid token
            stash_id: Stash tab ID
            league: League name
            
        Returns:
            Stash tab contents
        """
        try:
            headers = {'Authorization': f'Bearer {account.access_token}'}
            
            url = f"{self.API_BASE_URL}/stash/{account.account_name}/{stash_id}"
            params = {'league': league}
            
            response = requests.get(url, headers=headers, params=params)
            response.raise_for_status()
            
            return response.json()
            
        except Exception as e:
            logger.error(f"Error getting stash tab contents: {e}")
            return None
    
    def _get_current_league(self, account: POEAccount) -> Optional[str]:
        """Get current league for account"""
        try:
            headers = {'Authorization': f'Bearer {account.access_token}'}
            response = requests.get(f"{self.API_BASE_URL}/character", headers=headers)
            response.raise_for_status()
            
            characters = response.json()
            if characters:
                # Return league of first character (most recent)
                return characters[0].get('league')
                
        except Exception as e:
            logger.error(f"Error getting current league: {e}")
            
        return None
