"""
Comprehensive demo of the POE Currency Analysis Tool

This script demonstrates all the features of the currency analysis system.
"""

import time
from datetime import datetime
from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager
from poe_currency_analyzer import CurrencyAnalyzer
import j<PERSON>


def demo_basic_functionality():
    """Demonstrate basic API and data collection functionality"""
    print("🔄 BASIC FUNCTIONALITY DEMO")
    print("=" * 50)
    
    # Initialize components
    api = POENinjaAPI(league="Standard")
    data_manager = CurrencyDataManager()
    
    # Fetch and display current data
    print("Fetching current currency data...")
    currency_data = api.get_currency_data('currency')
    
    if currency_data:
        lines = currency_data.get('lines', [])
        print(f"✓ Fetched {len(lines)} currency items")
        
        # Show top 5 most valuable currencies
        sorted_currencies = sorted(lines, key=lambda x: x.get('chaosEquivalent', 0), reverse=True)
        print("\nTop 5 Most Valuable Currencies:")
        for i, currency in enumerate(sorted_currencies[:5], 1):
            name = currency.get('currencyTypeName', 'Unknown')
            value = currency.get('chaosEquivalent', 0)
            change = currency.get('receiveSparkLine', {}).get('totalChange', 0)
            trend = "📈" if change > 0 else "📉" if change < 0 else "➡️"
            print(f"  {i}. {name:<25} {value:>10.2f} chaos {trend}")
    
    print()


def demo_data_collection():
    """Demonstrate automated data collection"""
    print("💾 DATA COLLECTION DEMO")
    print("=" * 50)
    
    data_manager = CurrencyDataManager()
    
    # Show existing snapshots
    snapshots = data_manager.get_all_snapshots()
    print(f"Current snapshots: {len(snapshots)}")
    
    for snapshot in snapshots:
        timestamp = snapshot['timestamp']
        currency_count = len(snapshot['data'].get('currency', {}).get('lines', []))
        fragment_count = len(snapshot['data'].get('fragment', {}).get('lines', []))
        print(f"  📸 {timestamp}: {currency_count} currencies, {fragment_count} fragments")
    
    print()


def demo_analysis_features():
    """Demonstrate analysis capabilities"""
    print("📊 ANALYSIS FEATURES DEMO")
    print("=" * 50)
    
    data_manager = CurrencyDataManager()
    analyzer = CurrencyAnalyzer(data_manager)
    
    # Get historical data
    df = analyzer.get_historical_data(days=30)
    print(f"Historical data points: {len(df)}")
    
    if not df.empty:
        print(f"Unique currencies tracked: {df['name'].nunique()}")
        print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
        
        # Calculate price changes
        df = analyzer.calculate_price_changes(df)
        
        # Show currencies with data
        currencies_with_data = df[df['price_change'].notna()]
        if not currencies_with_data.empty:
            print(f"Currencies with price change data: {len(currencies_with_data)}")
            
            # Find opportunities
            opportunities = analyzer.find_profit_opportunities(df, min_change=1.0)
            print(f"Potential trading opportunities: {len(opportunities)}")
            
            if not opportunities.empty:
                print("\nTop Trading Opportunities:")
                for _, opp in opportunities.head(3).iterrows():
                    print(f"  {opp['opportunity_type']} {opp['name']:<20} "
                          f"{opp['price_change']:>6.1f}% ({opp['chaos_equivalent']:>8.1f} chaos)")
        
        # Generate comprehensive report
        report = analyzer.generate_analysis_report(days=7)
        if 'error' not in report:
            print(f"\n📈 Market Summary:")
            print(f"  Total market value: {report['total_market_value']:,.0f} chaos")
            print(f"  Tracked currencies: {report['total_currencies']}")
            print(f"  Analysis period: {report['period_days']} days")
    
    print()


def demo_specific_currency_analysis():
    """Demonstrate analysis of specific currencies"""
    print("🎯 SPECIFIC CURRENCY ANALYSIS")
    print("=" * 50)
    
    data_manager = CurrencyDataManager()
    analyzer = CurrencyAnalyzer(data_manager)
    
    # Get data for analysis
    df = analyzer.get_historical_data(days=30)
    
    if not df.empty:
        # Find some interesting currencies to analyze
        latest_data = df.groupby('name').last().reset_index()
        valuable_currencies = latest_data[latest_data['chaos_equivalent'] > 100].nlargest(5, 'chaos_equivalent')
        
        print("Analysis of valuable currencies:")
        for _, currency in valuable_currencies.iterrows():
            name = currency['name']
            value = currency['chaos_equivalent']
            
            # Get historical data for this currency
            currency_history = df[df['name'] == name].sort_values('timestamp')
            
            if len(currency_history) > 1:
                price_change = ((currency_history['chaos_equivalent'].iloc[-1] - 
                               currency_history['chaos_equivalent'].iloc[0]) / 
                               currency_history['chaos_equivalent'].iloc[0] * 100)
                print(f"  📊 {name:<25} {value:>10.2f} chaos (change: {price_change:>6.1f}%)")
            else:
                print(f"  📊 {name:<25} {value:>10.2f} chaos (insufficient data)")
    
    print()


def demo_export_functionality():
    """Demonstrate data export capabilities"""
    print("💾 EXPORT FUNCTIONALITY DEMO")
    print("=" * 50)
    
    data_manager = CurrencyDataManager()
    analyzer = CurrencyAnalyzer(data_manager)
    
    # Generate and save analysis report
    report = analyzer.generate_analysis_report(days=7)
    
    if 'error' not in report:
        # Save report to file
        report_filename = f"poe_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_filename, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"✓ Analysis report saved to: {report_filename}")
        
        # Show report summary
        print(f"  📊 Report contains:")
        print(f"    - {len(report.get('top_gainers', []))} top gainers")
        print(f"    - {len(report.get('top_losers', []))} top losers")
        print(f"    - {len(report.get('opportunities', []))} trading opportunities")
    
    print()


def main():
    """Run the complete demo"""
    print("🎮 PATH OF EXILE CURRENCY ANALYSIS TOOL DEMO")
    print("=" * 60)
    print("This demo showcases the capabilities of your private POE currency analysis tool.")
    print("Similar to poe.ninja but focused on personal analytics and trading insights.")
    print()
    
    try:
        # Run all demo sections
        demo_basic_functionality()
        demo_data_collection()
        demo_analysis_features()
        demo_specific_currency_analysis()
        demo_export_functionality()
        
        print("✅ DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("Your POE currency analysis tool is ready to use!")
        print()
        print("Next steps:")
        print("1. Run 'python poe_data_collector.py' to start collecting data regularly")
        print("2. Use the analysis features to identify trading opportunities")
        print("3. Integrate with your Flask web app for a nice UI")
        print("4. Set up alerts for significant price movements")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
