#!/usr/bin/env python3
"""
POE Currency Analysis App Launcher

This script launches the POE currency analysis web application from the correct directory.
"""

import os
import sys

# Add the current directory to Python path so imports work
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Change to the poe directory
os.chdir(current_dir)

# Import and run the app
from poe_app import app

if __name__ == '__main__':
    print("🎮 Starting POE Currency Analysis Web App")
    print("=" * 50)
    print("Access the application at: http://localhost:5006")
    print("Features:")
    print("  - Real-time currency prices")
    print("  - Historical price analysis")
    print("  - Trading opportunity detection")
    print("  - Interactive charts and visualizations")
    print("  - Data collection management")
    print()
    
    app.run(debug=True, port=5006, host='localhost')
