"""
POE Currency Investment Calculator Module

This module provides investment calculation capabilities for Path of Exile currencies,
including profit/loss calculations, portfolio management, and investment scenario analysis.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Union
import json
import os
from dataclasses import dataclass, asdict
from poe_get_currency_prices import CurrencyDataManager


@dataclass
class Investment:
    """Represents a single currency investment"""
    currency_name: str
    amount_invested: float  # Amount in chaos orbs
    quantity_bought: float  # Quantity of the currency bought
    buy_price: float  # Price per unit when bought (in chaos)
    buy_timestamp: datetime
    target_sell_price: Optional[float] = None  # Target price to sell (in chaos)
    stop_loss_price: Optional[float] = None  # Stop loss price (in chaos)
    notes: str = ""
    
    def to_dict(self) -> Dict:
        """Convert investment to dictionary"""
        data = asdict(self)
        data['buy_timestamp'] = self.buy_timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Investment':
        """Create investment from dictionary"""
        data['buy_timestamp'] = datetime.fromisoformat(data['buy_timestamp'])
        return cls(**data)


@dataclass
class InvestmentResult:
    """Represents the result of an investment calculation"""
    investment: Investment
    current_price: float
    current_value: float  # Current value in chaos orbs
    profit_loss: float  # Profit/loss in chaos orbs
    profit_loss_percentage: float
    days_held: int
    daily_return: float  # Daily return percentage
    status: str  # 'profit', 'loss', 'break_even'
    
    def to_dict(self) -> Dict:
        """Convert result to dictionary"""
        result = asdict(self)
        result['investment'] = self.investment.to_dict()
        return result


class InvestmentCalculator:
    """Advanced investment calculator for POE currencies"""
    
    def __init__(self, data_manager: CurrencyDataManager):
        """
        Initialize the investment calculator
        
        Args:
            data_manager: CurrencyDataManager instance for price data
        """
        self.data_manager = data_manager
        self.portfolio_file = "poe_data/investment_portfolio.json"
        
    def calculate_investment_scenario(self, 
                                    investment_amount: float,
                                    target_currency: str,
                                    current_price: Optional[float] = None) -> Dict:
        """
        Calculate investment scenario for a given amount and target currency
        
        Args:
            investment_amount: Amount to invest (in chaos orbs)
            target_currency: Currency to invest in
            current_price: Current price per unit (if None, fetches latest)
            
        Returns:
            Dictionary with investment scenario details
        """
        if current_price is None:
            current_price = self._get_current_price(target_currency)
            
        if current_price is None or current_price <= 0:
            return {"error": f"Could not get valid price for {target_currency}"}
            
        # Calculate how much of the currency can be bought
        quantity_buyable = investment_amount / current_price
        
        # Get historical data for analysis
        historical_data = self._get_currency_history(target_currency, days=30)
        
        scenario = {
            "currency_name": target_currency,
            "investment_amount": investment_amount,
            "current_price": current_price,
            "quantity_buyable": quantity_buyable,
            "timestamp": datetime.now().isoformat()
        }
        
        if not historical_data.empty:
            # Calculate statistics
            price_stats = self._calculate_price_statistics(historical_data)
            scenario.update(price_stats)
            
            # Calculate potential scenarios
            scenario["scenarios"] = self._calculate_price_scenarios(
                investment_amount, current_price, price_stats
            )
            
        return scenario
    
    def create_investment(self,
                         currency_name: str,
                         amount_invested: float,
                         target_sell_price: Optional[float] = None,
                         stop_loss_price: Optional[float] = None,
                         notes: str = "") -> Investment:
        """
        Create a new investment record
        
        Args:
            currency_name: Name of the currency to invest in
            amount_invested: Amount invested in chaos orbs
            target_sell_price: Optional target sell price
            stop_loss_price: Optional stop loss price
            notes: Optional notes about the investment
            
        Returns:
            Investment object
        """
        current_price = self._get_current_price(currency_name)
        
        if current_price is None or current_price <= 0:
            raise ValueError(f"Could not get valid price for {currency_name}")
            
        quantity_bought = amount_invested / current_price
        
        investment = Investment(
            currency_name=currency_name,
            amount_invested=amount_invested,
            quantity_bought=quantity_bought,
            buy_price=current_price,
            buy_timestamp=datetime.now(),
            target_sell_price=target_sell_price,
            stop_loss_price=stop_loss_price,
            notes=notes
        )
        
        return investment
    
    def calculate_investment_performance(self, investment: Investment) -> InvestmentResult:
        """
        Calculate current performance of an investment
        
        Args:
            investment: Investment to analyze
            
        Returns:
            InvestmentResult with current performance
        """
        current_price = self._get_current_price(investment.currency_name)
        
        if current_price is None:
            raise ValueError(f"Could not get current price for {investment.currency_name}")
            
        current_value = investment.quantity_bought * current_price
        profit_loss = current_value - investment.amount_invested
        profit_loss_percentage = (profit_loss / investment.amount_invested) * 100
        
        days_held = (datetime.now() - investment.buy_timestamp).days
        if days_held == 0:
            days_held = 1  # Avoid division by zero
            
        daily_return = profit_loss_percentage / days_held
        
        # Determine status
        if profit_loss > 0:
            status = "profit"
        elif profit_loss < 0:
            status = "loss"
        else:
            status = "break_even"
            
        return InvestmentResult(
            investment=investment,
            current_price=current_price,
            current_value=current_value,
            profit_loss=profit_loss,
            profit_loss_percentage=profit_loss_percentage,
            days_held=days_held,
            daily_return=daily_return,
            status=status
        )

    def save_investment_to_portfolio(self, investment: Investment) -> bool:
        """
        Save an investment to the portfolio file

        Args:
            investment: Investment to save

        Returns:
            True if saved successfully
        """
        try:
            # Ensure data directory exists
            os.makedirs(os.path.dirname(self.portfolio_file), exist_ok=True)

            # Load existing portfolio
            portfolio = self.load_portfolio()

            # Add new investment
            portfolio.append(investment.to_dict())

            # Save portfolio
            with open(self.portfolio_file, 'w') as f:
                json.dump(portfolio, f, indent=2)

            return True

        except Exception as e:
            print(f"Error saving investment: {e}")
            return False

    def load_portfolio(self) -> List[Dict]:
        """
        Load portfolio from file

        Returns:
            List of investment dictionaries
        """
        try:
            if os.path.exists(self.portfolio_file):
                with open(self.portfolio_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"Error loading portfolio: {e}")
            return []

    def get_portfolio_performance(self) -> Dict:
        """
        Calculate performance of entire portfolio

        Returns:
            Dictionary with portfolio performance metrics
        """
        portfolio_data = self.load_portfolio()

        if not portfolio_data:
            return {"error": "No investments in portfolio"}

        investments = [Investment.from_dict(data) for data in portfolio_data]
        results = []

        total_invested = 0
        total_current_value = 0
        total_profit_loss = 0

        for investment in investments:
            try:
                result = self.calculate_investment_performance(investment)
                results.append(result)

                total_invested += investment.amount_invested
                total_current_value += result.current_value
                total_profit_loss += result.profit_loss

            except Exception as e:
                print(f"Error calculating performance for {investment.currency_name}: {e}")
                continue

        if total_invested == 0:
            return {"error": "No valid investments found"}

        portfolio_return = (total_profit_loss / total_invested) * 100

        # Calculate additional metrics
        profitable_investments = len([r for r in results if r.status == "profit"])
        losing_investments = len([r for r in results if r.status == "loss"])

        return {
            "total_investments": len(results),
            "total_invested": total_invested,
            "total_current_value": total_current_value,
            "total_profit_loss": total_profit_loss,
            "portfolio_return_percentage": portfolio_return,
            "profitable_investments": profitable_investments,
            "losing_investments": losing_investments,
            "win_rate": (profitable_investments / len(results)) * 100 if results else 0,
            "investments": [result.to_dict() for result in results],
            "timestamp": datetime.now().isoformat()
        }

    def find_investment_opportunities(self,
                                    budget: float,
                                    min_expected_return: float = 10.0,
                                    max_risk_level: str = "medium") -> List[Dict]:
        """
        Find potential investment opportunities based on criteria

        Args:
            budget: Available budget in chaos orbs
            min_expected_return: Minimum expected return percentage
            max_risk_level: Maximum risk level (low, medium, high)

        Returns:
            List of investment opportunities
        """
        # Get latest snapshot for current prices
        latest_snapshot = self.data_manager.get_latest_snapshot()

        if not latest_snapshot or 'currency' not in latest_snapshot['data']:
            return []

        df = self.data_manager.convert_to_dataframe(
            latest_snapshot['data']['currency'], 'currency'
        )

        opportunities = []

        for _, currency in df.iterrows():
            if currency['chaos_equivalent'] <= 0:
                continue

            # Skip if can't afford at least 1 unit
            if currency['chaos_equivalent'] > budget:
                continue

            # Get historical data for analysis
            historical_data = self._get_currency_history(currency['name'], days=30)

            if historical_data.empty:
                continue

            # Calculate opportunity metrics
            opportunity = self._analyze_investment_opportunity(
                currency, historical_data, budget, min_expected_return
            )

            if opportunity and opportunity.get('expected_return', 0) >= min_expected_return:
                opportunities.append(opportunity)

        # Sort by expected return
        opportunities.sort(key=lambda x: x.get('expected_return', 0), reverse=True)

        return opportunities[:10]  # Return top 10 opportunities

    def _get_current_price(self, currency_name: str) -> Optional[float]:
        """Get current price for a currency"""
        latest_snapshot = self.data_manager.get_latest_snapshot()

        if not latest_snapshot:
            return None

        # Check currency data
        if 'currency' in latest_snapshot['data']:
            df = self.data_manager.convert_to_dataframe(
                latest_snapshot['data']['currency'], 'currency'
            )
            currency_data = df[df['name'] == currency_name]
            if not currency_data.empty:
                return float(currency_data['chaos_equivalent'].iloc[0])

        # Check fragment data
        if 'fragment' in latest_snapshot['data']:
            df = self.data_manager.convert_to_dataframe(
                latest_snapshot['data']['fragment'], 'fragment'
            )
            currency_data = df[df['name'] == currency_name]
            if not currency_data.empty:
                return float(currency_data['chaos_equivalent'].iloc[0])

        return None

    def _get_currency_history(self, currency_name: str, days: int = 30) -> pd.DataFrame:
        """Get historical data for a specific currency"""
        snapshots = self.data_manager.get_all_snapshots()

        if not snapshots:
            return pd.DataFrame()

        records = []
        cutoff_date = datetime.now() - timedelta(days=days)

        for snapshot in snapshots:
            timestamp = datetime.fromisoformat(snapshot['timestamp'])

            if timestamp < cutoff_date:
                continue

            # Check currency data
            if 'currency' in snapshot['data']:
                df = self.data_manager.convert_to_dataframe(
                    snapshot['data']['currency'], 'currency'
                )
                currency_data = df[df['name'] == currency_name]
                if not currency_data.empty:
                    record = currency_data.iloc[0].copy()
                    record['timestamp'] = timestamp
                    records.append(record)
                    continue

            # Check fragment data
            if 'fragment' in snapshot['data']:
                df = self.data_manager.convert_to_dataframe(
                    snapshot['data']['fragment'], 'fragment'
                )
                currency_data = df[df['name'] == currency_name]
                if not currency_data.empty:
                    record = currency_data.iloc[0].copy()
                    record['timestamp'] = timestamp
                    records.append(record)

        if not records:
            return pd.DataFrame()

        return pd.DataFrame(records).sort_values('timestamp')

    def _calculate_price_statistics(self, historical_data: pd.DataFrame) -> Dict:
        """Calculate price statistics from historical data"""
        if historical_data.empty:
            return {}

        prices = historical_data['chaos_equivalent']

        # Calculate price changes
        price_changes = prices.pct_change().dropna() * 100

        return {
            "min_price": float(prices.min()),
            "max_price": float(prices.max()),
            "avg_price": float(prices.mean()),
            "current_price": float(prices.iloc[-1]),
            "volatility": float(prices.std()),
            "avg_daily_change": float(price_changes.mean()),
            "max_daily_gain": float(price_changes.max()) if not price_changes.empty else 0,
            "max_daily_loss": float(price_changes.min()) if not price_changes.empty else 0,
            "price_trend": "up" if prices.iloc[-1] > prices.iloc[0] else "down",
            "total_return": float(((prices.iloc[-1] / prices.iloc[0]) - 1) * 100) if prices.iloc[0] > 0 else 0
        }

    def _calculate_price_scenarios(self,
                                 investment_amount: float,
                                 current_price: float,
                                 price_stats: Dict) -> Dict:
        """Calculate different price scenarios for investment"""
        quantity = investment_amount / current_price

        scenarios = {}

        # Conservative scenario (10% gain)
        conservative_price = current_price * 1.10
        scenarios["conservative"] = {
            "price_target": conservative_price,
            "expected_value": quantity * conservative_price,
            "expected_profit": (quantity * conservative_price) - investment_amount,
            "expected_return": 10.0
        }

        # Moderate scenario (25% gain)
        moderate_price = current_price * 1.25
        scenarios["moderate"] = {
            "price_target": moderate_price,
            "expected_value": quantity * moderate_price,
            "expected_profit": (quantity * moderate_price) - investment_amount,
            "expected_return": 25.0
        }

        # Aggressive scenario (50% gain)
        aggressive_price = current_price * 1.50
        scenarios["aggressive"] = {
            "price_target": aggressive_price,
            "expected_value": quantity * aggressive_price,
            "expected_profit": (quantity * aggressive_price) - investment_amount,
            "expected_return": 50.0
        }

        # Historical high scenario
        if price_stats.get("max_price"):
            max_price = price_stats["max_price"]
            scenarios["historical_high"] = {
                "price_target": max_price,
                "expected_value": quantity * max_price,
                "expected_profit": (quantity * max_price) - investment_amount,
                "expected_return": ((max_price / current_price) - 1) * 100
            }

        return scenarios

    def _analyze_investment_opportunity(self,
                                      currency: pd.Series,
                                      historical_data: pd.DataFrame,
                                      budget: float,
                                      min_expected_return: float) -> Optional[Dict]:
        """Analyze a single currency for investment opportunity"""
        try:
            price_stats = self._calculate_price_statistics(historical_data)

            if not price_stats:
                return None

            current_price = currency['chaos_equivalent']
            quantity_buyable = budget / current_price

            # Calculate expected return based on historical performance
            avg_return = price_stats.get('total_return', 0)
            volatility = price_stats.get('volatility', 0)

            # Simple risk assessment
            if volatility < current_price * 0.1:
                risk_level = "low"
            elif volatility < current_price * 0.3:
                risk_level = "medium"
            else:
                risk_level = "high"

            opportunity = {
                "currency_name": currency['name'],
                "current_price": current_price,
                "quantity_buyable": quantity_buyable,
                "investment_amount": budget,
                "expected_return": avg_return,
                "risk_level": risk_level,
                "volatility": volatility,
                "price_stats": price_stats,
                "recommendation": "BUY" if avg_return > min_expected_return else "HOLD"
            }

            return opportunity

        except Exception as e:
            print(f"Error analyzing opportunity for {currency['name']}: {e}")
            return None
