{% extends "poe_base.html" %}

{% block title %}POE Currency Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-coins"></i> Currency Market Dashboard</h1>
        <p class="text-muted">Real-time Path of Exile currency prices and market analysis</p>
        
        {% if error %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> Error: {{ error }}
        </div>
        {% endif %}
        
        {% if last_updated %}
        <div class="alert alert-info">
            <i class="fas fa-clock"></i> Last updated: {{ last_updated }}
        </div>
        {% endif %}
    </div>
</div>

<!-- Market Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-coins"></i> Total Currencies</h5>
                <h3 class="currency-value">{{ analysis_summary.get('total_currencies', 0) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-chart-line"></i> Opportunities</h5>
                <h3 class="currency-value">{{ analysis_summary.get('opportunities_count', 0) }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-arrow-up"></i> Top Gainers</h5>
                <h3 class="price-up">{{ analysis_summary.get('top_gainers', [])|length }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title"><i class="fas fa-arrow-down"></i> Top Losers</h5>
                <h3 class="price-down">{{ analysis_summary.get('top_losers', [])|length }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Current Prices -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Top Currencies by Value</h5>
            </div>
            <div class="card-body">
                {% if current_data %}
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Currency</th>
                                <th>Value (Chaos)</th>
                                <th>Trend</th>
                                <th>Change</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for currency in current_data %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td>{{ currency.name }}</td>
                                <td class="currency-value">{{ "%.2f"|format(currency.chaos_equivalent) }}</td>
                                <td>
                                    {% if currency.pay_total_change %}
                                        {% if currency.pay_total_change > 0 %}📈
                                        {% elif currency.pay_total_change < 0 %}📉
                                        {% else %}➡️{% endif %}
                                    {% else %}➡️{% endif %}
                                </td>
                                <td>
                                    {% if currency.pay_total_change %}
                                        <span class="{% if currency.pay_total_change > 0 %}price-up{% elif currency.pay_total_change < 0 %}price-down{% else %}price-neutral{% endif %}">
                                            {{ "%.1f"|format(currency.pay_total_change) }}%
                                        </span>
                                    {% else %}
                                        <span class="price-neutral">N/A</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-database fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No currency data available.</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>League Issue:</strong> The current league might not have active trading data.
                        <br><small>Try switching to "Standard" league in the code or collect data from an active league.</small>
                    </div>
                    <button class="btn btn-primary" onclick="collectData()">
                        <i class="fas fa-sync-alt"></i> Collect Data Now
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Analysis Summary -->
    <div class="col-lg-4">
        <!-- Top Gainers -->
        {% if analysis_summary.get('top_gainers') %}
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-arrow-up"></i> Top Gainers</h6>
            </div>
            <div class="card-body">
                {% for gainer in analysis_summary.top_gainers[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small>{{ gainer.name[:20] }}{% if gainer.name|length > 20 %}...{% endif %}</small>
                    <span class="price-up">+{{ "%.1f"|format(gainer.price_change) }}%</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Top Losers -->
        {% if analysis_summary.get('top_losers') %}
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="fas fa-arrow-down"></i> Top Losers</h6>
            </div>
            <div class="card-body">
                {% for loser in analysis_summary.top_losers[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <small>{{ loser.name[:20] }}{% if loser.name|length > 20 %}...{% endif %}</small>
                    <span class="price-down">{{ "%.1f"|format(loser.price_change) }}%</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        <!-- Trading Opportunities -->
        {% if analysis_summary.get('opportunities') %}
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-bullseye"></i> Trading Opportunities</h6>
            </div>
            <div class="card-body">
                {% for opp in analysis_summary.opportunities[:3] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <small>{{ opp.name[:15] }}{% if opp.name|length > 15 %}...{% endif %}</small>
                        <br>
                        <span class="badge {% if opp.opportunity_type == 'BUY' %}bg-success{% elif opp.opportunity_type == 'SELL' %}bg-danger{% else %}bg-secondary{% endif %}">
                            {{ opp.opportunity_type }}
                        </span>
                    </div>
                    <span class="{% if opp.price_change > 0 %}price-up{% else %}price-down{% endif %}">
                        {{ "%.1f"|format(opp.price_change) }}%
                    </span>
                </div>
                {% endfor %}
                {% if analysis_summary.opportunities|length > 3 %}
                <div class="text-center mt-2">
                    <a href="/analysis" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
        
        <!-- Investment Quick Tools -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-calculator"></i> Investment Tools</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="quickInvestAmount" class="form-label small">Quick Investment (Chaos)</label>
                    <input type="number" class="form-control form-control-sm" id="quickInvestAmount"
                           placeholder="Amount" min="1" step="1" value="100">
                </div>
                <div class="mb-3">
                    <label for="quickInvestCurrency" class="form-label small">Target Currency</label>
                    <select class="form-select form-select-sm" id="quickInvestCurrency">
                        <option value="">Select currency...</option>
                        {% for currency in current_data[:10] %}
                        <option value="{{ currency.name }}">{{ currency.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-success btn-sm" onclick="quickInvestmentCalculation()">
                        <i class="fas fa-calculator"></i> Quick Calculate
                    </button>
                    <a href="/investment" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-coins"></i> Full Investment Tool
                    </a>
                </div>

                <!-- Quick calculation results -->
                <div id="quickInvestResults" class="mt-3" style="display: none;">
                    <div class="alert alert-info alert-sm">
                        <div id="quickInvestContent"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stash Quick Access -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-archive"></i> Stash Tools</h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <i class="fas fa-search fa-2x text-info mb-2"></i>
                    <p class="small text-muted mb-2">Analyze your stash for valuable items</p>
                </div>

                <div class="d-grid gap-2">
                    <a href="/stash" class="btn btn-info btn-sm">
                        <i class="fas fa-archive"></i> Stash Analyzer
                    </a>
                    <button class="btn btn-outline-info btn-sm" onclick="checkStashValue()" disabled>
                        <i class="fas fa-coins"></i> Quick Value Check
                    </button>
                </div>

                <div id="stashQuickInfo" class="mt-3" style="display: none;">
                    <div class="alert alert-info alert-sm">
                        <div id="stashQuickContent"></div>
                    </div>
                </div>

                <div class="mt-2">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        Link your POE account to use stash analysis
                    </small>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-tools"></i> Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/analysis" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-chart-line"></i> Detailed Analysis
                    </a>
                    <a href="/charts" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-bar"></i> View Charts
                    </a>
                    <a href="/data-collection" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-database"></i> Manage Data
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Portfolio Overview Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-briefcase"></i> Portfolio Overview</h5>
                <a href="/investment" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-external-link-alt"></i> Full Portfolio
                </a>
            </div>
            <div class="card-body">
                <div id="portfolioOverview">
                    <div class="text-center">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2">Loading portfolio...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Auto-refresh notice -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> 
            <strong>Tip:</strong> This dashboard shows real-time data from poe.ninja. 
            Use the "Collect Data" button to fetch the latest prices, or set up automated collection 
            in the <a href="/data-collection" class="alert-link">Data Collection</a> section.
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh data every 5 minutes
    setInterval(function() {
        const now = new Date();
        const minutes = now.getMinutes();
        
        // Only auto-refresh at 5-minute intervals to avoid spam
        if (minutes % 5 === 0 && now.getSeconds() < 10) {
            console.log('Auto-refreshing data...');
            collectData();
        }
    }, 10000); // Check every 10 seconds
    
    // Add tooltips to currency names
    document.addEventListener('DOMContentLoaded', function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Load portfolio overview
        loadPortfolioOverview();
    });

    // Quick investment calculation
    function quickInvestmentCalculation() {
        const amount = document.getElementById('quickInvestAmount').value;
        const currency = document.getElementById('quickInvestCurrency').value;

        if (!amount || !currency) {
            alert('Please enter amount and select currency');
            return;
        }

        fetch('/api/investment/scenario', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                investment_amount: parseFloat(amount),
                target_currency: currency
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert('Error: ' + data.error);
                return;
            }

            displayQuickInvestResults(data);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to calculate investment scenario');
        });
    }

    function displayQuickInvestResults(data) {
        const resultsDiv = document.getElementById('quickInvestResults');
        const contentDiv = document.getElementById('quickInvestContent');

        let html = `
            <strong>${data.currency_name}</strong><br>
            <small>
                Price: ${data.current_price.toFixed(2)} chaos<br>
                Quantity: ${data.quantity_buyable.toFixed(4)} units<br>
        `;

        if (data.scenarios && data.scenarios.conservative) {
            const conservative = data.scenarios.conservative;
            html += `Expected profit (10%): <span class="price-up">+${conservative.expected_profit.toFixed(2)} chaos</span>`;
        }

        html += `</small>`;

        contentDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
    }

    // Load portfolio overview
    function loadPortfolioOverview() {
        fetch('/api/investment/portfolio')
        .then(response => response.json())
        .then(data => {
            displayPortfolioOverview(data);
        })
        .catch(error => {
            console.error('Error loading portfolio:', error);
            document.getElementById('portfolioOverview').innerHTML =
                '<div class="text-muted text-center">Failed to load portfolio</div>';
        });
    }

    function displayPortfolioOverview(portfolio) {
        const overviewDiv = document.getElementById('portfolioOverview');

        if (portfolio.error || !portfolio.total_investments || portfolio.total_investments === 0) {
            overviewDiv.innerHTML = `
                <div class="text-center text-muted">
                    <i class="fas fa-briefcase fa-2x mb-2"></i>
                    <p>No investments yet. <a href="/investment">Start investing</a> to track your portfolio here.</p>
                </div>
            `;
            return;
        }

        const returnClass = portfolio.portfolio_return_percentage >= 0 ? 'price-up' : 'price-down';
        const returnIcon = portfolio.portfolio_return_percentage >= 0 ? '📈' : '📉';

        let html = `
            <div class="row text-center">
                <div class="col-md-3">
                    <h6 class="mb-1">Total Invested</h6>
                    <h5 class="currency-value">${portfolio.total_invested.toFixed(2)}</h5>
                    <small class="text-muted">Chaos Orbs</small>
                </div>
                <div class="col-md-3">
                    <h6 class="mb-1">Current Value</h6>
                    <h5 class="currency-value">${portfolio.total_current_value.toFixed(2)}</h5>
                    <small class="text-muted">Chaos Orbs</small>
                </div>
                <div class="col-md-3">
                    <h6 class="mb-1">P&L</h6>
                    <h5 class="${returnClass}">${returnIcon} ${portfolio.total_profit_loss.toFixed(2)}</h5>
                    <small class="text-muted">Chaos Orbs</small>
                </div>
                <div class="col-md-3">
                    <h6 class="mb-1">Return</h6>
                    <h5 class="${returnClass}">${portfolio.portfolio_return_percentage.toFixed(1)}%</h5>
                    <small class="text-muted">${portfolio.total_investments} investments</small>
                </div>
            </div>
        `;

        // Show top 3 investments
        if (portfolio.investments && portfolio.investments.length > 0) {
            html += `
                <hr>
                <h6 class="mb-2">Recent Investments</h6>
                <div class="row">
            `;

            portfolio.investments.slice(0, 3).forEach(investment => {
                const investReturnClass = investment.profit_loss_percentage >= 0 ? 'price-up' : 'price-down';
                html += `
                    <div class="col-md-4">
                        <div class="small">
                            <strong>${investment.investment.currency_name}</strong><br>
                            <span class="${investReturnClass}">
                                ${investment.profit_loss_percentage.toFixed(1)}%
                            </span>
                            (${investment.days_held}d)
                        </div>
                    </div>
                `;
            });

            html += '</div>';
        }

        overviewDiv.innerHTML = html;
    }

    // Stash quick value check
    function checkStashValue() {
        // This would check if account is linked and show quick stash info
        alert('Feature coming soon! Use the full Stash Analyzer for now.');
    }
</script>
{% endblock %}
