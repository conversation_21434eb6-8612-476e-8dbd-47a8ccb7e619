{% extends "poe_base.html" %}

{% block title %}POE Currency Analysis{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-chart-line"></i> Currency Market Analysis</h1>
        <p class="text-muted">Detailed analysis and trading insights for Path of Exile currencies</p>
    </div>
</div>

<!-- Analysis Period Selector -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6>Analysis Period</h6>
                <div class="btn-group" role="group">
                    <a href="/analysis?days=1" class="btn {% if days == 1 %}btn-primary{% else %}btn-outline-primary{% endif %} btn-sm">1 Day</a>
                    <a href="/analysis?days=7" class="btn {% if days == 7 %}btn-primary{% else %}btn-outline-primary{% endif %} btn-sm">7 Days</a>
                    <a href="/analysis?days=30" class="btn {% if days == 30 %}btn-primary{% else %}btn-outline-primary{% endif %} btn-sm">30 Days</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6>Analysis Date</h6>
                <p class="mb-0">{{ report.get('analysis_date', 'N/A') }}</p>
            </div>
        </div>
    </div>
</div>

{% if report.get('error') %}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle"></i> Error: {{ report.error }}
</div>
{% else %}

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="currency-value">{{ report.get('total_currencies', 0) }}</h5>
                <p class="card-text">Total Currencies</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="currency-value">{{ "%.0f"|format(report.get('total_market_value', 0)) }}</h5>
                <p class="card-text">Market Value (Chaos)</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="{% if report.get('average_price_change', 0) > 0 %}price-up{% elif report.get('average_price_change', 0) < 0 %}price-down{% else %}price-neutral{% endif %}">
                    {{ "%.2f"|format(report.get('average_price_change', 0)) }}%
                </h5>
                <p class="card-text">Avg Price Change</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="currency-value">{{ report.get('profit_opportunities', 0) }}</h5>
                <p class="card-text">Trading Opportunities</p>
            </div>
        </div>
    </div>
</div>

<!-- Top Gainers and Losers -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-arrow-up"></i> Top Gainers</h5>
            </div>
            <div class="card-body">
                {% if report.get('top_gainers') %}
                <div class="table-responsive">
                    <table class="table table-dark table-sm">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th>Value</th>
                                <th>Change</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for gainer in report.top_gainers %}
                            <tr>
                                <td>{{ gainer.name[:25] }}{% if gainer.name|length > 25 %}...{% endif %}</td>
                                <td class="currency-value">{{ "%.2f"|format(gainer.chaos_equivalent) }}</td>
                                <td class="price-up">+{{ "%.1f"|format(gainer.price_change) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No gainers data available</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-arrow-down"></i> Top Losers</h5>
            </div>
            <div class="card-body">
                {% if report.get('top_losers') %}
                <div class="table-responsive">
                    <table class="table table-dark table-sm">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th>Value</th>
                                <th>Change</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for loser in report.top_losers %}
                            <tr>
                                <td>{{ loser.name[:25] }}{% if loser.name|length > 25 %}...{% endif %}</td>
                                <td class="currency-value">{{ "%.2f"|format(loser.chaos_equivalent) }}</td>
                                <td class="price-down">{{ "%.1f"|format(loser.price_change) }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">No losers data available</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Trading Opportunities -->
{% if report.get('opportunities') %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bullseye"></i> Trading Opportunities</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-dark">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th>Current Value</th>
                                <th>Price Change</th>
                                <th>Absolute Change</th>
                                <th>Recommendation</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for opp in report.opportunities %}
                            <tr>
                                <td>{{ opp.name }}</td>
                                <td class="currency-value">{{ "%.2f"|format(opp.chaos_equivalent) }}</td>
                                <td class="{% if opp.price_change > 0 %}price-up{% else %}price-down{% endif %}">
                                    {{ "%.1f"|format(opp.price_change) }}%
                                </td>
                                <td class="{% if opp.price_change_abs > 0 %}price-up{% else %}price-down{% endif %}">
                                    {{ "%.2f"|format(opp.price_change_abs) }}
                                </td>
                                <td>
                                    <span class="badge {% if opp.opportunity_type == 'BUY' %}bg-success{% elif opp.opportunity_type == 'SELL' %}bg-danger{% else %}bg-secondary{% endif %}">
                                        {{ opp.opportunity_type }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> No trading opportunities detected for the current analysis period.
            Try adjusting the time period or collecting more data.
        </div>
    </div>
</div>
{% endif %}

{% endif %}

<!-- Export Options -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-download"></i> Export Analysis</h6>
            </div>
            <div class="card-body">
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="exportAnalysis('json')">
                        <i class="fas fa-file-code"></i> Export JSON
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportAnalysis('csv')">
                        <i class="fas fa-file-csv"></i> Export CSV
                    </button>
                    <a href="/charts" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-chart-bar"></i> View Charts
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function exportAnalysis(format) {
        const data = {{ report|tojson }};
        
        if (format === 'json') {
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `poe_analysis_${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        } else if (format === 'csv') {
            // Simple CSV export for opportunities
            if (data.opportunities && data.opportunities.length > 0) {
                let csv = 'Currency,Value,Price Change,Absolute Change,Recommendation\n';
                data.opportunities.forEach(opp => {
                    csv += `"${opp.name}",${opp.chaos_equivalent},${opp.price_change},${opp.price_change_abs},"${opp.opportunity_type}"\n`;
                });
                
                const blob = new Blob([csv], { type: 'text/csv' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `poe_opportunities_${new Date().toISOString().split('T')[0]}.csv`;
                a.click();
                URL.revokeObjectURL(url);
            } else {
                showAlert('warning', 'No opportunities data available for CSV export');
            }
        }
    }
</script>
{% endblock %}
