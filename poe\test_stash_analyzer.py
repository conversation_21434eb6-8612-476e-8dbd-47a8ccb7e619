"""
Test script for POE Stash Analyzer

This script tests the stash analysis components without requiring
a real POE account connection.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poe_stash_scanner import StashScanner, StashItem
from poe_stash_pricer import StashPricer
from datetime import datetime

def create_test_items():
    """Create test stash items for analysis"""
    test_items = []
    
    # Test currency items
    chaos_orb = StashItem(
        name="Chaos Orb",
        base_type="Chaos Orb",
        item_level=1,
        rarity="Currency",
        category="Currency",
        stack_size=50,
        position=(0, 0),
        properties={},
        requirements={},
        sockets=[],
        influences=[],
        synthesised=False,
        fractured=False,
        corrupted=False,
        identified=True,
        note="",
        raw_data={}
    )
    test_items.append(chaos_orb)
    
    # Test divine orb
    divine_orb = StashItem(
        name="Divine Orb",
        base_type="Divine Orb",
        item_level=1,
        rarity="Currency",
        category="Currency",
        stack_size=3,
        position=(1, 0),
        properties={},
        requirements={},
        sockets=[],
        influences=[],
        synthesised=False,
        fractured=False,
        corrupted=False,
        identified=True,
        note="",
        raw_data={}
    )
    test_items.append(divine_orb)
    
    # Test unique item
    unique_weapon = StashItem(
        name="Doryani's Catalyst",
        base_type="Vaal Sceptre",
        item_level=68,
        rarity="Unique",
        category="Weapons",
        stack_size=1,
        position=(2, 0),
        properties={"Physical Damage": "50-93", "Critical Strike Chance": "6.50%"},
        requirements={"Level": "68", "Str": "113", "Int": "113"},
        sockets=[],
        influences=[],
        synthesised=False,
        fractured=False,
        corrupted=False,
        identified=True,
        note="~price 5 chaos",
        raw_data={}
    )
    test_items.append(unique_weapon)
    
    # Test gem
    skill_gem = StashItem(
        name="Raise Spectre",
        base_type="Raise Spectre",
        item_level=1,
        rarity="Gem",
        category="Gems",
        stack_size=1,
        position=(3, 0),
        properties={"Level": "20", "Quality": "20"},
        requirements={"Level": "28", "Dex": "58", "Int": "111"},
        sockets=[],
        influences=[],
        synthesised=False,
        fractured=False,
        corrupted=True,
        identified=True,
        note="",
        raw_data={}
    )
    test_items.append(skill_gem)
    
    # Test rare equipment
    rare_armor = StashItem(
        name="Bone Circlet",
        base_type="Bone Circlet",
        item_level=85,
        rarity="Rare",
        category="Armour",
        stack_size=1,
        position=(4, 0),
        properties={"Energy Shield": "150"},
        requirements={"Level": "73", "Int": "155"},
        sockets=[],
        influences=["Shaper"],
        synthesised=False,
        fractured=False,
        corrupted=False,
        identified=True,
        note="",
        raw_data={}
    )
    test_items.append(rare_armor)
    
    return test_items

def test_stash_scanner():
    """Test the stash scanner functionality"""
    print("🔍 Testing Stash Scanner")
    print("-" * 30)
    
    scanner = StashScanner()
    test_items = create_test_items()
    
    # Test item categorization
    categorized = scanner.categorize_items(test_items)
    print(f"✅ Categorized {len(test_items)} items into {len(categorized)} categories")
    
    for category, items in categorized.items():
        print(f"   {category}: {len(items)} items")
    
    # Test valuable item filtering
    valuable_items = scanner.filter_valuable_items(test_items)
    print(f"✅ Found {len(valuable_items)} valuable items")
    
    for item in valuable_items:
        print(f"   {item.name} ({item.category}) - {item.rarity}")
    
    # Test stash summary
    summary = scanner.get_stash_summary(test_items)
    print(f"✅ Stash summary generated:")
    print(f"   Total items: {summary['total_items']}")
    print(f"   Valuable items: {summary['valuable_items']}")
    print(f"   Categories: {list(summary['category_counts'].keys())}")

def test_stash_pricer():
    """Test the stash pricer functionality"""
    print(f"\n💰 Testing Stash Pricer")
    print("-" * 30)
    
    try:
        pricer = StashPricer(league="Mercenaries")
        test_items = create_test_items()
        
        print(f"✅ Pricer initialized with market data:")
        market_summary = pricer.get_market_summary()
        print(f"   Currency prices: {market_summary['currency_count']}")
        print(f"   Unique items: {market_summary['unique_count']}")
        print(f"   Gems: {market_summary['gem_count']}")
        
        # Test individual item pricing
        print(f"\n📊 Individual Item Pricing:")
        for item in test_items:
            try:
                price = pricer.price_item(item)
                print(f"   {item.name}: {price.estimated_value:.2f} chaos ({price.confidence} confidence)")
            except Exception as e:
                print(f"   {item.name}: Error - {e}")
        
        # Test stash value analysis
        print(f"\n📈 Stash Value Analysis:")
        stash_value = pricer.analyze_stash_value(test_items)
        print(f"   Total value: {stash_value.total_value:.2f} chaos")
        print(f"   Currency value: {stash_value.currency_value:.2f} chaos")
        print(f"   Equipment value: {stash_value.equipment_value:.2f} chaos")
        print(f"   Other value: {stash_value.other_value:.2f} chaos")
        
        # Test sell recommendations
        print(f"\n🎯 Sell Recommendations:")
        recommendations = pricer.get_sell_recommendations(test_items, min_value=1.0)
        print(f"   Found {len(recommendations)} items to sell:")
        
        for rec in recommendations[:5]:  # Top 5
            item = rec['item']
            price = rec['price']
            priority = rec['sell_priority']
            reason = rec['recommendation_reason']
            
            print(f"   {item.name}: {price.estimated_value:.2f} chaos (Priority: {priority:.1f})")
            print(f"      Reason: {reason}")
        
    except Exception as e:
        print(f"❌ Error testing pricer: {e}")
        import traceback
        traceback.print_exc()

def test_integration():
    """Test full integration workflow"""
    print(f"\n🔧 Testing Full Integration")
    print("-" * 30)
    
    try:
        # Simulate a complete stash analysis workflow
        scanner = StashScanner()
        pricer = StashPricer(league="Mercenaries")
        
        # Create test stash data
        test_items = create_test_items()
        
        # Step 1: Scan items
        summary = scanner.get_stash_summary(test_items)
        
        # Step 2: Analyze value
        stash_value = pricer.analyze_stash_value(test_items)
        
        # Step 3: Get recommendations
        recommendations = pricer.get_sell_recommendations(test_items)
        
        # Step 4: Generate report
        print(f"✅ Complete Analysis Report:")
        print(f"   📦 Items scanned: {summary['total_items']}")
        print(f"   💎 Valuable items: {summary['valuable_items']}")
        print(f"   💰 Total value: {stash_value.total_value:.2f} chaos")
        print(f"   🎯 Sell recommendations: {len(recommendations)}")
        
        # Top recommendation
        if recommendations:
            top_rec = recommendations[0]
            print(f"   🏆 Top sell recommendation: {top_rec['item'].name} ({top_rec['price'].estimated_value:.2f} chaos)")
        
        print(f"✅ Integration test completed successfully!")
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all stash analyzer tests"""
    print("🧪 POE Stash Analyzer Test Suite")
    print("=" * 50)
    
    test_stash_scanner()
    test_stash_pricer()
    test_integration()
    
    print(f"\n🎉 Test Suite Complete!")
    print(f"\n💡 Next Steps:")
    print("1. Set up POE API credentials (see STASH_ANALYZER_SETUP.md)")
    print("2. Start the Flask app: python poe_app.py")
    print("3. Visit: http://localhost:5006/stash")
    print("4. Link your POE account and analyze real stash tabs!")

if __name__ == "__main__":
    main()
