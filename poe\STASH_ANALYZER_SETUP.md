# POE Stash Analyzer Setup Guide

## 🎯 Overview

The POE Stash Analyzer allows you to:
- **Link your POE account** securely using OAuth
- **Scan your stash tabs** for valuable items
- **Get real-time price estimates** for all your items
- **Receive sell recommendations** with priority scoring
- **Track total stash value** across all tabs

## 🔧 Setup Instructions

### Step 1: Create POE API Application

1. **Visit the POE Developer Portal**: https://www.pathofexile.com/developer/api-keys
2. **Log in** with your Path of Exile account
3. **Create a new OAuth Application** with these settings:
   - **Application Name**: `POE Currency Analyzer`
   - **Redirect URI**: `http://localhost:5006/auth/callback`
   - **Scopes**: Select `account:stashes` and `account:characters`

4. **Copy your credentials**:
   - Client ID (e.g., `your_client_id_here`)
   - Client Secret (e.g., `your_client_secret_here`)

### Step 2: Configure Environment Variables

Create a `.env` file in your `poe` directory or set environment variables:

```bash
# Windows (PowerShell)
$env:POE_CLIENT_ID="your_client_id_here"
$env:POE_CLIENT_SECRET="your_client_secret_here"

# Linux/Mac
export POE_CLIENT_ID="your_client_id_here"
export POE_CLIENT_SECRET="your_client_secret_here"
```

### Step 3: Update Application Configuration

Edit `poe_app.py` and replace the placeholder values:

```python
# Replace these lines:
POE_CLIENT_ID = os.getenv('POE_CLIENT_ID', 'your_client_id_here')
POE_CLIENT_SECRET = os.getenv('POE_CLIENT_SECRET', 'your_client_secret_here')

# With your actual credentials:
POE_CLIENT_ID = os.getenv('POE_CLIENT_ID', 'your_actual_client_id')
POE_CLIENT_SECRET = os.getenv('POE_CLIENT_SECRET', 'your_actual_client_secret')
```

## 🚀 How to Use

### 1. Link Your Account
1. **Start the application**: `python poe_app.py`
2. **Visit**: http://localhost:5006/stash
3. **Click "Link POE Account"** - you'll be redirected to pathofexile.com
4. **Authorize the application** - you'll be redirected back
5. **Success!** Your account is now linked

### 2. Analyze Stash Tabs
1. **Select your league** (Mercenaries, Standard, etc.)
2. **Choose a stash tab** from the dropdown
3. **Click "Analyze Stash Tab"** - analysis takes 5-10 seconds
4. **View results**:
   - Total stash value in chaos orbs
   - Most valuable items
   - Sell recommendations with priority
   - Category breakdown

### 3. Sell Recommendations
The analyzer provides intelligent sell recommendations based on:
- **Item value** (higher value = higher priority)
- **Market confidence** (reliable prices get priority)
- **Liquidity** (currency sells faster than equipment)
- **Player notes** (already priced items get priority)

## 📊 Features

### Price Analysis
- **Currency**: Real-time prices from poe.ninja
- **Unique Items**: Market prices for all unique weapons, armor, accessories
- **Gems**: Prices based on level, quality, and corruption
- **Maps**: Tier-based pricing
- **Player Notes**: Parses your own price notes

### Value Categories
- **High Confidence**: Currency, unique items with market data
- **Medium Confidence**: Gems, maps, estimated uniques
- **Low Confidence**: Rare items, estimated values

### Sell Priority Scoring
Items are scored based on:
- Base value (higher = better)
- Price confidence multiplier
- Category liquidity bonus
- Player note bonus

## 🔒 Security & Privacy

### What We Access
- ✅ **Read-only access** to your stash tabs
- ✅ **Character information** (for league detection)
- ❌ **Cannot modify** your account or items
- ❌ **Cannot trade** or interact with other players

### Data Storage
- **Account tokens** are stored locally in `poe_data/poe_account.json`
- **No item data** is permanently stored
- **Analysis results** are temporary and not saved

### Revoking Access
You can revoke access at any time:
1. **POE Website**: Account Settings → Applications → Revoke
2. **Local**: Delete `poe_data/poe_account.json`

## 🛠️ Troubleshooting

### "Configuration Needed" Error
- **Cause**: POE_CLIENT_ID or POE_CLIENT_SECRET not set
- **Fix**: Set environment variables or update poe_app.py

### "Authentication Failed" Error
- **Cause**: Invalid client credentials or redirect URI mismatch
- **Fix**: Check your POE API application settings

### "No Stash Tabs Found" Error
- **Cause**: League has no characters or stash tabs
- **Fix**: Try a different league or create a character

### "Failed to Fetch Stash Tab" Error
- **Cause**: API rate limiting or network issues
- **Fix**: Wait a moment and try again

## 📈 Example Analysis Results

```
💰 Total Stash Value: 1,247.50 chaos orbs

🏆 Most Valuable Items:
1. Mirror of Kalandra: 197,137 chaos (High confidence)
2. Divine Orb x5: 1,029 chaos (High confidence)
3. Mageblood: 850 chaos (High confidence)

🎯 Top Sell Recommendations:
1. Ancient Orb x10: 45.5 chaos (Priority: 59.2)
2. Exalted Orb x3: 12.6 chaos (Priority: 16.4)
3. Unique Ring: 8.2 chaos (Priority: 9.8)

📊 Category Breakdown:
- Currency: 1,100 chaos (88%)
- Equipment: 120 chaos (10%)
- Other: 27.5 chaos (2%)
```

## 🎉 Ready to Use!

Your POE Stash Analyzer is now fully configured and ready to help you identify valuable items and optimize your selling strategy!

Visit: http://localhost:5006/stash
