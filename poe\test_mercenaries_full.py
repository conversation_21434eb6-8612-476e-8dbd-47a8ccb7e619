from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager
from poe_investment_calculator import InvestmentCalculator

# Test full Mercenaries league functionality
print("🧪 Testing Mercenaries League Full Functionality")
print("=" * 50)

# Step 1: Fetch data
api = POENinjaAPI(league="Mercenaries")
currency_data = api.get_currency_data('currency')
fragment_data = api.get_currency_data('fragment')

if currency_data:
    print(f"✅ Currency data: {len(currency_data['lines'])} items")
    
    # Step 2: Save snapshot
    dm = CurrencyDataManager()
    all_data = {'currency': currency_data}
    if fragment_data:
        all_data['fragment'] = fragment_data
        print(f"✅ Fragment data: {len(fragment_data['lines'])} items")
    
    snapshot_path = dm.save_currency_snapshot(all_data)
    print(f"✅ Saved snapshot: {snapshot_path}")
    
    # Step 3: Test investment calculator
    calculator = InvestmentCalculator(dm)
    
    # Test scenario calculation
    scenario = calculator.calculate_investment_scenario(100.0, "Divine Orb")
    
    if 'error' not in scenario:
        print(f"✅ Investment calculator working!")
        print(f"   💰 100 chaos → {scenario['quantity_buyable']:.4f} Divine Orbs")
        print(f"   💎 Price: {scenario['current_price']:.2f} chaos")
        
        if 'scenarios' in scenario:
            conservative = scenario['scenarios']['conservative']
            print(f"   📈 Conservative profit: +{conservative['expected_profit']:.2f} chaos")
    else:
        print(f"❌ Investment error: {scenario['error']}")
        
    # Step 4: Test DataFrame conversion
    df = dm.convert_to_dataframe(currency_data, 'currency')
    print(f"✅ DataFrame: {df.shape[0]} rows, {df.shape[1]} columns")
    
    if not df.empty and 'chaos_equivalent' in df.columns:
        top_5 = df.nlargest(5, 'chaos_equivalent')[['name', 'chaos_equivalent']]
        print(f"🏆 Top 5 currencies:")
        for _, row in top_5.iterrows():
            print(f"   {row['name']}: {row['chaos_equivalent']:.2f} chaos")
    
    print(f"\n🎉 Mercenaries league is fully functional!")
    
else:
    print("❌ Failed to fetch Mercenaries data")
