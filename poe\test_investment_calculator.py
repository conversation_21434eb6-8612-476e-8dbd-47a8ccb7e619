"""
Test script for the POE Investment Calculator

This script tests the core functionality of the investment calculator
to ensure all features work correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from poe_get_currency_prices import CurrencyDataManager
from poe_investment_calculator import InvestmentCalculator, Investment
from datetime import datetime
import json

def test_investment_calculator():
    """Test the investment calculator functionality"""
    print("🧪 Testing POE Investment Calculator")
    print("=" * 50)
    
    # Initialize components
    data_manager = CurrencyDataManager()
    calculator = InvestmentCalculator(data_manager)
    
    # Test 1: Calculate investment scenario
    print("\n1. Testing Investment Scenario Calculation...")
    try:
        scenario = calculator.calculate_investment_scenario(
            investment_amount=100.0,
            target_currency="Divine Orb"
        )
        
        if 'error' in scenario:
            print(f"   ⚠️  Warning: {scenario['error']}")
        else:
            print(f"   ✅ Scenario calculated successfully")
            print(f"   📊 Currency: {scenario['currency_name']}")
            print(f"   💰 Investment: {scenario['investment_amount']} chaos")
            print(f"   💎 Current Price: {scenario['current_price']:.2f} chaos")
            print(f"   📦 Quantity Buyable: {scenario['quantity_buyable']:.4f}")
            
            if 'scenarios' in scenario:
                print(f"   📈 Conservative profit: {scenario['scenarios']['conservative']['expected_profit']:.2f} chaos")
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 2: Create investment
    print("\n2. Testing Investment Creation...")
    try:
        investment = calculator.create_investment(
            currency_name="Chaos Orb",
            amount_invested=50.0,
            target_sell_price=1.2,
            stop_loss_price=0.8,
            notes="Test investment"
        )
        
        print(f"   ✅ Investment created successfully")
        print(f"   📊 Currency: {investment.currency_name}")
        print(f"   💰 Amount: {investment.amount_invested} chaos")
        print(f"   📦 Quantity: {investment.quantity_bought:.4f}")
        print(f"   💎 Buy Price: {investment.buy_price:.2f} chaos")
        
        # Test saving to portfolio
        success = calculator.save_investment_to_portfolio(investment)
        if success:
            print(f"   ✅ Investment saved to portfolio")
        else:
            print(f"   ⚠️  Warning: Failed to save investment")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 3: Portfolio performance
    print("\n3. Testing Portfolio Performance...")
    try:
        performance = calculator.get_portfolio_performance()
        
        if 'error' in performance:
            print(f"   ⚠️  Warning: {performance['error']}")
        else:
            print(f"   ✅ Portfolio performance calculated")
            print(f"   📊 Total Investments: {performance['total_investments']}")
            print(f"   💰 Total Invested: {performance['total_invested']:.2f} chaos")
            print(f"   📈 Total P&L: {performance['total_profit_loss']:.2f} chaos")
            print(f"   📊 Portfolio Return: {performance['portfolio_return_percentage']:.1f}%")
            print(f"   🎯 Win Rate: {performance['win_rate']:.1f}%")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    # Test 4: Find investment opportunities
    print("\n4. Testing Investment Opportunities...")
    try:
        opportunities = calculator.find_investment_opportunities(
            budget=100.0,
            min_expected_return=5.0
        )
        
        print(f"   ✅ Found {len(opportunities)} investment opportunities")
        
        if opportunities:
            print("   📊 Top 3 opportunities:")
            for i, opp in enumerate(opportunities[:3], 1):
                print(f"      {i}. {opp['currency_name']}: {opp['expected_return']:.1f}% return ({opp['risk_level']} risk)")
                
    except Exception as e:
        print(f"   ❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Investment Calculator Test Complete!")
    print("\n💡 Tips for using the investment calculator:")
    print("   • Use the web interface at http://localhost:5006/investment")
    print("   • Start with small amounts to test strategies")
    print("   • Set target prices and stop losses for risk management")
    print("   • Monitor your portfolio regularly")
    print("   • Diversify across different currency types")

def test_investment_scenarios():
    """Test different investment scenarios"""
    print("\n🎯 Testing Investment Scenarios")
    print("-" * 30)
    
    data_manager = CurrencyDataManager()
    calculator = InvestmentCalculator(data_manager)
    
    # Test scenarios with different amounts
    test_amounts = [10, 50, 100, 500]
    test_currencies = ["Divine Orb", "Exalted Orb", "Ancient Orb"]
    
    for amount in test_amounts:
        print(f"\n💰 Testing {amount} chaos investment:")
        
        for currency in test_currencies:
            try:
                scenario = calculator.calculate_investment_scenario(amount, currency)
                
                if 'error' not in scenario:
                    print(f"   {currency}: {scenario['quantity_buyable']:.4f} units @ {scenario['current_price']:.2f} chaos")
                else:
                    print(f"   {currency}: {scenario['error']}")
                    
            except Exception as e:
                print(f"   {currency}: Error - {e}")

if __name__ == "__main__":
    test_investment_calculator()
    test_investment_scenarios()
