
<!DOCTYPE html>
<html>
<head>
    <title>POE Currency Analysis Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .chart-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .chart-card { background: #f9f9f9; padding: 15px; border-radius: 5px; border: 1px solid #ddd; }
        .chart-card h3 { margin-top: 0; color: #444; }
        .chart-link { display: inline-block; background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px; }
        .chart-link:hover { background: #0056b3; }
        .stats { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Path of Exile Currency Analysis Dashboard</h1>
        <p style="text-align: center; color: #666;">Your private currency price analysis tool - Similar to poe.ninja but for personal analytics</p>
        
        <div class="stats">
            <h3>📊 Quick Stats</h3>
            <p><strong>Top Currencies Tracked:</strong> 5</p>
            <p><strong>Analysis Date:</strong> 2025-07-18 23:00:41.333015</p>
        </div>
        
        <h2>📈 Market Overview</h2>
        <div class="chart-card">
            <h3>Overall Market Snapshot</h3>
            <p>View the top 20 currencies by value in the current market.</p>
            <a href="market_overview.html" class="chart-link" target="_blank">View Market Overview</a>
        </div>
        
        <h2>💰 Top Currency Trends</h2>
        <div class="chart-grid">

            <div class="chart-card">
                <h3>Mirror of Kalandra</h3>
                <p><strong>Current Value:</strong> 357,862.72 chaos</p>
                <p>View detailed price trend and historical data for this currency.</p>
                <a href="trend_Mirror_of_Kalandra.html" class="chart-link" target="_blank">View Trend</a>
            </div>
        
            <div class="chart-card">
                <h3>Hinekora's Lock</h3>
                <p><strong>Current Value:</strong> 83,509.09 chaos</p>
                <p>View detailed price trend and historical data for this currency.</p>
                <a href="trend_Hinekoras_Lock.html" class="chart-link" target="_blank">View Trend</a>
            </div>
        
            <div class="chart-card">
                <h3>Mirror Shard</h3>
                <p><strong>Current Value:</strong> 18,584.40 chaos</p>
                <p>View detailed price trend and historical data for this currency.</p>
                <a href="trend_Mirror_Shard.html" class="chart-link" target="_blank">View Trend</a>
            </div>
        
            <div class="chart-card">
                <h3>Reflecting Mist</h3>
                <p><strong>Current Value:</strong> 6,859.39 chaos</p>
                <p>View detailed price trend and historical data for this currency.</p>
                <a href="trend_Reflecting_Mist.html" class="chart-link" target="_blank">View Trend</a>
            </div>
        
            <div class="chart-card">
                <h3>Tainted Divine Teardrop</h3>
                <p><strong>Current Value:</strong> 4,199.62 chaos</p>
                <p>View detailed price trend and historical data for this currency.</p>
                <a href="trend_Tainted_Divine_Teardrop.html" class="chart-link" target="_blank">View Trend</a>
            </div>
        
        </div>
        
        <h2>🔧 Tools & Features</h2>
        <div class="chart-grid">
            <div class="chart-card">
                <h3>Data Collection</h3>
                <p>Automated data collection from poe.ninja API with configurable intervals.</p>
                <code>python poe_data_collector.py</code>
            </div>
            <div class="chart-card">
                <h3>Analysis Engine</h3>
                <p>Advanced analysis including trend detection, profit opportunities, and market insights.</p>
                <code>python poe_demo.py</code>
            </div>
            <div class="chart-card">
                <h3>Export & Reports</h3>
                <p>Generate detailed JSON reports and export data for further analysis.</p>
                <code>python test_analyzer.py</code>
            </div>
        </div>
        
        <div class="footer">
            <p>🛠️ Built with Python, Pandas, and Plotly | 📊 Data from poe.ninja API</p>
            <p>This tool is not affiliated with or endorsed by Grinding Gear Games.</p>
        </div>
    </div>
</body>
</html>
