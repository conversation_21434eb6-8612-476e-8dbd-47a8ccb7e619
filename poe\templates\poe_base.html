<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}POE Currency Analyzer{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #1a1a1a;
            color: #e0e0e0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background-color: #2d2d2d !important;
            border-bottom: 2px solid #8B4513;
        }
        
        .navbar-brand {
            color: #DAA520 !important;
            font-weight: bold;
        }
        
        .nav-link {
            color: #e0e0e0 !important;
        }
        
        .nav-link:hover {
            color: #DAA520 !important;
        }
        
        .card {
            background-color: #2d2d2d;
            border: 1px solid #444;
            color: #e0e0e0;
        }
        
        .card-header {
            background-color: #3d3d3d;
            border-bottom: 1px solid #555;
        }
        
        .table-dark {
            background-color: #2d2d2d;
        }
        
        .btn-primary {
            background-color: #8B4513;
            border-color: #8B4513;
        }
        
        .btn-primary:hover {
            background-color: #A0522D;
            border-color: #A0522D;
        }
        
        .btn-success {
            background-color: #228B22;
            border-color: #228B22;
        }
        
        .btn-danger {
            background-color: #DC143C;
            border-color: #DC143C;
        }
        
        .alert-info {
            background-color: #1e3a5f;
            border-color: #2e5a8f;
            color: #b3d9ff;
        }
        
        .alert-success {
            background-color: #1e5f1e;
            border-color: #2e8f2e;
            color: #b3ffb3;
        }
        
        .alert-warning {
            background-color: #5f5f1e;
            border-color: #8f8f2e;
            color: #ffffb3;
        }
        
        .alert-danger {
            background-color: #5f1e1e;
            border-color: #8f2e2e;
            color: #ffb3b3;
        }
        
        .currency-value {
            font-weight: bold;
            color: #DAA520;
        }
        
        .price-up {
            color: #00ff00;
        }
        
        .price-down {
            color: #ff4444;
        }
        
        .price-neutral {
            color: #888;
        }
        
        .footer {
            background-color: #2d2d2d;
            border-top: 1px solid #444;
            margin-top: 50px;
            padding: 20px 0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner-border {
            color: #DAA520;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-coins"></i> POE Currency Analyzer
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/"><i class="fas fa-home"></i> Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analysis"><i class="fas fa-chart-line"></i> Analysis</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/investment"><i class="fas fa-calculator"></i> Investment</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stash"><i class="fas fa-archive"></i> Stash Analyzer</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/charts"><i class="fas fa-chart-bar"></i> Charts</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/data-collection"><i class="fas fa-database"></i> Data Collection</a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-success btn-sm" onclick="collectData()">
                            <i class="fas fa-sync-alt"></i> Collect Data
                        </button>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">
                        <i class="fas fa-coins"></i> POE Currency Analyzer
                        <small class="text-muted">- Personal Analytics Tool</small>
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <small class="text-muted">
                        Data from poe.ninja API | Not affiliated with Grinding Gear Games
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Function to collect data
        function collectData() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Collecting...';
            button.disabled = true;
            
            fetch('/api/collect', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', `Data collected successfully! ${data.currency_count} currencies, ${data.fragment_count} fragments`);
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showAlert('danger', 'Failed to collect data: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                showAlert('danger', 'Error collecting data: ' + error.message);
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        // Function to show alerts
        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);
            
            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }
        
        // Format numbers with commas
        function formatNumber(num) {
            return num.toLocaleString();
        }
        
        // Format currency values
        function formatCurrency(value) {
            if (value >= 1000000) {
                return (value / 1000000).toFixed(1) + 'M';
            } else if (value >= 1000) {
                return (value / 1000).toFixed(1) + 'K';
            } else {
                return value.toFixed(2);
            }
        }
        
        // Get price change indicator
        function getPriceChangeIndicator(change) {
            if (change > 0) return '📈';
            if (change < 0) return '📉';
            return '➡️';
        }
        
        // Get price change class
        function getPriceChangeClass(change) {
            if (change > 0) return 'price-up';
            if (change < 0) return 'price-down';
            return 'price-neutral';
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
