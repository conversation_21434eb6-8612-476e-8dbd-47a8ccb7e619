"""
Advanced Path of Exile Currency Analysis Module

This module provides advanced analysis capabilities for POE currency data,
including trend analysis, profit opportunity detection, and historical comparisons.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import json
import os
from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager


class CurrencyAnalyzer:
    """Advanced analysis tools for POE currency data"""
    
    def __init__(self, data_manager: CurrencyDataManager):
        """
        Initialize the analyzer
        
        Args:
            data_manager: CurrencyDataManager instance for data access
        """
        self.data_manager = data_manager
        
    def get_historical_data(self, days: int = 7) -> pd.DataFrame:
        """
        Get historical currency data for analysis
        
        Args:
            days: Number of days of history to retrieve
            
        Returns:
            DataFrame with historical currency data
        """
        snapshots = self.data_manager.get_all_snapshots()
        
        if not snapshots:
            return pd.DataFrame()
            
        # Convert snapshots to DataFrame
        all_records = []
        
        for snapshot in snapshots:
            timestamp = datetime.fromisoformat(snapshot['timestamp'])
            
            # Skip if older than requested days
            if (datetime.now() - timestamp).days > days:
                continue
                
            # Process currency data
            if 'currency' in snapshot['data']:
                df = self.data_manager.convert_to_dataframe(
                    snapshot['data']['currency'], 'currency'
                )
                df['timestamp'] = timestamp
                all_records.append(df)
                
            # Process fragment data
            if 'fragment' in snapshot['data']:
                df = self.data_manager.convert_to_dataframe(
                    snapshot['data']['fragment'], 'fragment'
                )
                df['timestamp'] = timestamp
                all_records.append(df)
                
        if not all_records:
            return pd.DataFrame()
            
        return pd.concat(all_records, ignore_index=True)
    
    def calculate_price_changes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate price changes over time for each currency
        
        Args:
            df: Historical currency data
            
        Returns:
            DataFrame with price change calculations
        """
        if df.empty:
            return df
            
        # Sort by currency and timestamp
        df = df.sort_values(['name', 'timestamp'])
        
        # Calculate price changes
        df['price_change'] = df.groupby('name')['chaos_equivalent'].pct_change() * 100
        df['price_change_abs'] = df.groupby('name')['chaos_equivalent'].diff()
        
        # Calculate moving averages
        df['ma_7'] = df.groupby('name')['chaos_equivalent'].rolling(window=7, min_periods=1).mean().reset_index(0, drop=True)
        df['ma_24'] = df.groupby('name')['chaos_equivalent'].rolling(window=24, min_periods=1).mean().reset_index(0, drop=True)
        
        return df
    
    def find_profit_opportunities(self, df: pd.DataFrame, min_change: float = 10.0) -> pd.DataFrame:
        """
        Find currencies with significant price movements that might indicate profit opportunities
        
        Args:
            df: Historical currency data with price changes
            min_change: Minimum percentage change to consider
            
        Returns:
            DataFrame with potential profit opportunities
        """
        if df.empty:
            return df
            
        # Get latest data for each currency
        latest_data = df.groupby('name').last().reset_index()
        
        # Filter for significant changes
        opportunities = latest_data[
            (abs(latest_data['price_change']) >= min_change) &
            (latest_data['chaos_equivalent'] > 1)  # Only consider valuable currencies
        ].copy()
        
        # Add opportunity type
        opportunities['opportunity_type'] = opportunities['price_change'].apply(
            lambda x: 'BUY' if x < -min_change else 'SELL' if x > min_change else 'HOLD'
        )
        
        # Sort by absolute price change
        opportunities = opportunities.sort_values('price_change', key=abs, ascending=False)
        
        return opportunities[['name', 'chaos_equivalent', 'price_change', 'price_change_abs', 'opportunity_type']]
    
    def get_top_movers(self, df: pd.DataFrame, n: int = 10) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Get top gaining and losing currencies
        
        Args:
            df: Historical currency data with price changes
            n: Number of top movers to return
            
        Returns:
            Tuple of (top_gainers, top_losers) DataFrames
        """
        if df.empty:
            return pd.DataFrame(), pd.DataFrame()
            
        latest_data = df.groupby('name').last().reset_index()
        
        # Filter out currencies with very low values to focus on meaningful changes
        filtered_data = latest_data[latest_data['chaos_equivalent'] > 0.1]
        
        top_gainers = filtered_data.nlargest(n, 'price_change')[
            ['name', 'chaos_equivalent', 'price_change', 'price_change_abs']
        ]
        
        top_losers = filtered_data.nsmallest(n, 'price_change')[
            ['name', 'chaos_equivalent', 'price_change', 'price_change_abs']
        ]
        
        return top_gainers, top_losers
    
    def create_price_trend_chart(self, currency_name: str, df: pd.DataFrame) -> go.Figure:
        """
        Create an interactive price trend chart for a specific currency
        
        Args:
            currency_name: Name of the currency to chart
            df: Historical currency data
            
        Returns:
            Plotly figure object
        """
        currency_data = df[df['name'] == currency_name].sort_values('timestamp')
        
        if currency_data.empty:
            fig = go.Figure()
            fig.add_annotation(text=f"No data found for {currency_name}", 
                             xref="paper", yref="paper", x=0.5, y=0.5)
            return fig
            
        fig = go.Figure()
        
        # Add price line
        fig.add_trace(go.Scatter(
            x=currency_data['timestamp'],
            y=currency_data['chaos_equivalent'],
            mode='lines+markers',
            name='Price',
            line=dict(color='blue', width=2),
            hovertemplate='<b>%{text}</b><br>Price: %{y:.2f} chaos<br>Time: %{x}<extra></extra>',
            text=[currency_name] * len(currency_data)
        ))
        
        # Add moving averages if available
        if 'ma_7' in currency_data.columns:
            fig.add_trace(go.Scatter(
                x=currency_data['timestamp'],
                y=currency_data['ma_7'],
                mode='lines',
                name='7-period MA',
                line=dict(color='orange', width=1, dash='dash'),
                opacity=0.7
            ))
            
        if 'ma_24' in currency_data.columns:
            fig.add_trace(go.Scatter(
                x=currency_data['timestamp'],
                y=currency_data['ma_24'],
                mode='lines',
                name='24-period MA',
                line=dict(color='red', width=1, dash='dot'),
                opacity=0.7
            ))
        
        fig.update_layout(
            title=f'{currency_name} Price Trend',
            xaxis_title='Time',
            yaxis_title='Price (Chaos Orbs)',
            hovermode='x unified',
            template='plotly_white'
        )
        
        return fig
    
    def create_market_overview_chart(self, df: pd.DataFrame) -> go.Figure:
        """
        Create a market overview chart showing top currencies by value
        
        Args:
            df: Historical currency data
            
        Returns:
            Plotly figure object
        """
        if df.empty:
            return go.Figure()
            
        # Get latest data for top currencies
        latest_data = df.groupby('name').last().reset_index()
        top_currencies = latest_data.nlargest(20, 'chaos_equivalent')
        
        # Create bar chart
        fig = go.Figure(data=[
            go.Bar(
                x=top_currencies['name'],
                y=top_currencies['chaos_equivalent'],
                text=top_currencies['chaos_equivalent'].round(2),
                textposition='auto',
                marker_color='lightblue'
            )
        ])
        
        fig.update_layout(
            title='Top 20 Currencies by Value',
            xaxis_title='Currency',
            yaxis_title='Value (Chaos Orbs)',
            xaxis_tickangle=-45,
            template='plotly_white'
        )
        
        return fig
    
    def generate_analysis_report(self, days: int = 7) -> Dict:
        """
        Generate a comprehensive analysis report
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        df = self.get_historical_data(days)
        
        if df.empty:
            return {"error": "No historical data available"}
            
        df = self.calculate_price_changes(df)
        
        # Get analysis results
        opportunities = self.find_profit_opportunities(df)
        top_gainers, top_losers = self.get_top_movers(df)
        
        # Calculate summary statistics
        latest_data = df.groupby('name').last().reset_index()
        
        report = {
            "analysis_date": datetime.now().isoformat(),
            "period_days": days,
            "total_currencies": len(latest_data),
            "total_market_value": latest_data['chaos_equivalent'].sum(),
            "average_price_change": latest_data['price_change'].mean(),
            "profit_opportunities": len(opportunities),
            "top_gainers": top_gainers.to_dict('records'),
            "top_losers": top_losers.to_dict('records'),
            "opportunities": opportunities.to_dict('records')
        }
        
        return report

    def analyze_investment_potential(self, currency_name: str, days: int = 30) -> Dict:
        """
        Analyze investment potential for a specific currency

        Args:
            currency_name: Name of the currency to analyze
            days: Number of days of history to analyze

        Returns:
            Dictionary with investment analysis
        """
        df = self.get_historical_data(days)

        if df.empty:
            return {"error": "No historical data available"}

        currency_data = df[df['name'] == currency_name].sort_values('timestamp')

        if currency_data.empty:
            return {"error": f"No data found for {currency_name}"}

        # Calculate price metrics
        prices = currency_data['chaos_equivalent']
        current_price = prices.iloc[-1]

        # Price statistics
        price_stats = {
            "current_price": float(current_price),
            "min_price": float(prices.min()),
            "max_price": float(prices.max()),
            "avg_price": float(prices.mean()),
            "median_price": float(prices.median()),
            "price_volatility": float(prices.std()),
            "price_range": float(prices.max() - prices.min())
        }

        # Calculate returns
        if len(prices) > 1:
            total_return = ((current_price / prices.iloc[0]) - 1) * 100
            daily_returns = prices.pct_change().dropna() * 100

            return_stats = {
                "total_return": float(total_return),
                "avg_daily_return": float(daily_returns.mean()),
                "daily_volatility": float(daily_returns.std()),
                "max_daily_gain": float(daily_returns.max()),
                "max_daily_loss": float(daily_returns.min()),
                "positive_days": int((daily_returns > 0).sum()),
                "negative_days": int((daily_returns < 0).sum()),
                "win_rate": float((daily_returns > 0).mean() * 100)
            }
        else:
            return_stats = {}

        # Risk assessment
        risk_score = self._calculate_risk_score(prices, daily_returns if len(prices) > 1 else None)

        # Investment recommendation
        recommendation = self._generate_investment_recommendation(
            price_stats, return_stats, risk_score
        )

        return {
            "currency_name": currency_name,
            "analysis_period_days": days,
            "price_statistics": price_stats,
            "return_statistics": return_stats,
            "risk_assessment": risk_score,
            "investment_recommendation": recommendation,
            "analysis_timestamp": datetime.now().isoformat()
        }

    def find_best_investment_currencies(self,
                                      min_price: float = 1.0,
                                      max_volatility: float = 50.0,
                                      min_volume_rank: int = 50) -> List[Dict]:
        """
        Find currencies with best investment potential

        Args:
            min_price: Minimum price in chaos orbs
            max_volatility: Maximum acceptable volatility
            min_volume_rank: Minimum volume rank (lower is better)

        Returns:
            List of currencies with investment analysis
        """
        df = self.get_historical_data(days=30)

        if df.empty:
            return []

        # Get latest data for each currency
        latest_data = df.groupby('name').last().reset_index()

        # Filter by criteria
        filtered_currencies = latest_data[
            (latest_data['chaos_equivalent'] >= min_price) &
            (latest_data['chaos_equivalent'] <= 1000)  # Reasonable upper limit
        ]

        investment_candidates = []

        for _, currency in filtered_currencies.iterrows():
            analysis = self.analyze_investment_potential(currency['name'], days=30)

            if 'error' in analysis:
                continue

            # Apply volatility filter
            volatility = analysis['price_statistics'].get('price_volatility', 0)
            if volatility > max_volatility:
                continue

            # Calculate investment score
            investment_score = self._calculate_investment_score(analysis)
            analysis['investment_score'] = investment_score

            investment_candidates.append(analysis)

        # Sort by investment score
        investment_candidates.sort(key=lambda x: x.get('investment_score', 0), reverse=True)

        return investment_candidates[:20]  # Return top 20

    def calculate_portfolio_diversification(self, investments: List[Dict]) -> Dict:
        """
        Calculate portfolio diversification metrics

        Args:
            investments: List of investment dictionaries

        Returns:
            Dictionary with diversification analysis
        """
        if not investments:
            return {"error": "No investments provided"}

        # Group by currency type/category (simplified)
        currency_types = {}
        total_value = 0

        for investment in investments:
            currency_name = investment.get('currency_name', '')
            value = investment.get('current_value', 0)
            total_value += value

            # Simple categorization (can be enhanced)
            if 'Divine' in currency_name or 'Mirror' in currency_name:
                category = 'High Value'
            elif 'Essence' in currency_name or 'Fossil' in currency_name:
                category = 'Crafting'
            elif 'Fragment' in currency_name or 'Splinter' in currency_name:
                category = 'Fragments'
            else:
                category = 'Standard'

            if category not in currency_types:
                currency_types[category] = {'count': 0, 'value': 0}
            currency_types[category]['count'] += 1
            currency_types[category]['value'] += value

        # Calculate diversification metrics
        diversification = {}
        for category, data in currency_types.items():
            diversification[category] = {
                'count': data['count'],
                'value': data['value'],
                'percentage': (data['value'] / total_value) * 100 if total_value > 0 else 0
            }

        # Calculate concentration risk (Herfindahl index)
        concentration_index = sum(
            (data['percentage'] / 100) ** 2 for data in diversification.values()
        )

        return {
            "total_investments": len(investments),
            "total_portfolio_value": total_value,
            "diversification_by_category": diversification,
            "concentration_index": concentration_index,
            "diversification_score": (1 - concentration_index) * 100,  # Higher is better
            "recommendation": "Well diversified" if concentration_index < 0.25 else
                           "Moderately diversified" if concentration_index < 0.5 else
                           "Highly concentrated"
        }

    def _calculate_risk_score(self, prices: pd.Series, daily_returns: Optional[pd.Series] = None) -> Dict:
        """Calculate risk assessment for a currency"""
        current_price = prices.iloc[-1]
        price_volatility = prices.std()

        # Price-based risk factors
        price_risk = {
            "volatility": float(price_volatility),
            "volatility_percentage": float((price_volatility / current_price) * 100),
            "price_stability": "high" if price_volatility < current_price * 0.1 else
                             "medium" if price_volatility < current_price * 0.3 else "low"
        }

        # Return-based risk factors
        if daily_returns is not None and len(daily_returns) > 0:
            return_risk = {
                "daily_volatility": float(daily_returns.std()),
                "max_drawdown": float(daily_returns.min()),
                "return_consistency": float(daily_returns.std() / abs(daily_returns.mean())) if daily_returns.mean() != 0 else float('inf')
            }
        else:
            return_risk = {}

        # Overall risk level
        volatility_pct = price_risk["volatility_percentage"]
        if volatility_pct < 10:
            risk_level = "low"
        elif volatility_pct < 30:
            risk_level = "medium"
        else:
            risk_level = "high"

        return {
            "risk_level": risk_level,
            "price_risk": price_risk,
            "return_risk": return_risk,
            "overall_score": volatility_pct  # Higher score = higher risk
        }

    def _generate_investment_recommendation(self,
                                          price_stats: Dict,
                                          return_stats: Dict,
                                          risk_score: Dict) -> Dict:
        """Generate investment recommendation based on analysis"""
        current_price = price_stats["current_price"]
        avg_price = price_stats["avg_price"]
        max_price = price_stats["max_price"]
        min_price = price_stats["min_price"]

        # Price position analysis
        price_position = (current_price - min_price) / (max_price - min_price) if max_price > min_price else 0.5

        # Return analysis
        total_return = return_stats.get("total_return", 0)
        win_rate = return_stats.get("win_rate", 50)

        # Risk analysis
        risk_level = risk_score["risk_level"]

        # Generate recommendation
        if price_position < 0.3 and total_return > 0 and risk_level in ["low", "medium"]:
            recommendation = "STRONG BUY"
            confidence = "high"
            reason = "Price near historical low with positive returns and acceptable risk"
        elif price_position < 0.5 and win_rate > 60 and risk_level != "high":
            recommendation = "BUY"
            confidence = "medium"
            reason = "Good price position with strong win rate"
        elif price_position > 0.8 or risk_level == "high":
            recommendation = "SELL"
            confidence = "medium"
            reason = "Price near historical high or high risk"
        elif total_return < -20:
            recommendation = "AVOID"
            confidence = "high"
            reason = "Poor historical performance"
        else:
            recommendation = "HOLD"
            confidence = "low"
            reason = "Mixed signals, monitor for better entry/exit points"

        return {
            "action": recommendation,
            "confidence": confidence,
            "reason": reason,
            "price_position": price_position,
            "target_buy_below": float(avg_price * 0.9),
            "target_sell_above": float(avg_price * 1.2),
            "stop_loss_suggestion": float(current_price * 0.85)
        }

    def _calculate_investment_score(self, analysis: Dict) -> float:
        """Calculate overall investment score (0-100)"""
        try:
            price_stats = analysis["price_statistics"]
            return_stats = analysis["return_statistics"]
            risk_score = analysis["risk_assessment"]
            recommendation = analysis["investment_recommendation"]

            score = 50  # Base score

            # Return factor (30% weight)
            total_return = return_stats.get("total_return", 0)
            if total_return > 20:
                score += 15
            elif total_return > 10:
                score += 10
            elif total_return > 0:
                score += 5
            elif total_return < -20:
                score -= 15

            # Win rate factor (20% weight)
            win_rate = return_stats.get("win_rate", 50)
            if win_rate > 70:
                score += 10
            elif win_rate > 60:
                score += 5
            elif win_rate < 40:
                score -= 10

            # Risk factor (25% weight)
            risk_level = risk_score["risk_level"]
            if risk_level == "low":
                score += 12
            elif risk_level == "medium":
                score += 5
            else:  # high risk
                score -= 10

            # Price position factor (15% weight)
            price_position = recommendation.get("price_position", 0.5)
            if price_position < 0.3:  # Near low
                score += 8
            elif price_position < 0.5:
                score += 4
            elif price_position > 0.8:  # Near high
                score -= 8

            # Recommendation factor (10% weight)
            action = recommendation.get("action", "HOLD")
            if action == "STRONG BUY":
                score += 5
            elif action == "BUY":
                score += 3
            elif action in ["SELL", "AVOID"]:
                score -= 5

            return max(0, min(100, score))  # Clamp between 0-100

        except Exception as e:
            print(f"Error calculating investment score: {e}")
            return 50  # Default neutral score
