{% extends "poe_base.html" %}

{% block title %}POE Data Collection{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-database"></i> Data Collection Management</h1>
        <p class="text-muted">Manage your currency price data collection and historical snapshots</p>
        
        {% if error %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> Error: {{ error }}
        </div>
        {% endif %}
    </div>
</div>

<!-- Collection Controls -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play"></i> Manual Collection</h5>
            </div>
            <div class="card-body">
                <p>Collect current currency prices from poe.ninja API</p>
                <button class="btn btn-primary" onclick="collectData()">
                    <i class="fas fa-sync-alt"></i> Collect Data Now
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Automated Collection</h5>
            </div>
            <div class="card-body">
                <p>Set up automated data collection using the command line tool</p>
                <code>python poe_data_collector.py --interval 1</code>
                <br><small class="text-muted">Collects data every hour</small>
            </div>
        </div>
    </div>
</div>

<!-- Data Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="currency-value">{{ snapshots|length }}</h4>
                <p class="card-text">Total Snapshots</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="currency-value">
                    {% if snapshots %}
                        {{ snapshots[-1].total_items }}
                    {% else %}
                        0
                    {% endif %}
                </h4>
                <p class="card-text">Items in Latest</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="currency-value">
                    {% if snapshots %}
                        {{ ((snapshots|length) * 24)|round|int }}
                    {% else %}
                        0
                    {% endif %}
                </h4>
                <p class="card-text">Est. Hours Coverage</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h4 class="currency-value">
                    {% if snapshots %}
                        {{ "%.1f"|format((snapshots|map(attribute='total_items')|sum) / (snapshots|length)) }}
                    {% else %}
                        0
                    {% endif %}
                </h4>
                <p class="card-text">Avg Items/Snapshot</p>
            </div>
        </div>
    </div>
</div>

<!-- Snapshot History -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> Snapshot History</h5>
            </div>
            <div class="card-body">
                {% if snapshots %}
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Currencies</th>
                                <th>Fragments</th>
                                <th>Total Items</th>
                                <th>Age</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for snapshot in snapshots|reverse %}
                            <tr>
                                <td>{{ snapshot.timestamp }}</td>
                                <td>{{ snapshot.currency_count }}</td>
                                <td>{{ snapshot.fragment_count }}</td>
                                <td class="currency-value">{{ snapshot.total_items }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ snapshot.timestamp }}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-database fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No data snapshots found. Start collecting data to see history here.</p>
                    <button class="btn btn-primary" onclick="collectData()">
                        <i class="fas fa-sync-alt"></i> Collect First Snapshot
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Collection Tips -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-lightbulb"></i> Collection Tips</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>📊 For Best Analysis</h6>
                        <ul class="small">
                            <li>Collect data every 1-2 hours</li>
                            <li>Maintain at least 7 days of history</li>
                            <li>Monitor during league events</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>⚡ Performance</h6>
                        <ul class="small">
                            <li>Each snapshot is ~1-2MB</li>
                            <li>API calls are rate-limited</li>
                            <li>Data is stored locally</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>🔧 Automation</h6>
                        <ul class="small">
                            <li>Use the command-line collector</li>
                            <li>Set up as a scheduled task</li>
                            <li>Monitor logs for errors</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Command Examples -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6><i class="fas fa-terminal"></i> Command Line Examples</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>One-time Collection</h6>
                        <code>python poe_data_collector.py --once</code>
                        <br><small class="text-muted">Collect data once and exit</small>
                    </div>
                    <div class="col-md-6">
                        <h6>Continuous Collection</h6>
                        <code>python poe_data_collector.py --interval 2</code>
                        <br><small class="text-muted">Collect every 2 hours</small>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Different League</h6>
                        <code>python poe_data_collector.py --league Hardcore</code>
                        <br><small class="text-muted">Collect from Hardcore league</small>
                    </div>
                    <div class="col-md-6">
                        <h6>View Logs</h6>
                        <code>tail -f poe_data_collector.log</code>
                        <br><small class="text-muted">Monitor collection logs</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-refresh the page every 30 seconds to show new snapshots
    setInterval(function() {
        // Only refresh if we're not actively interacting
        if (document.hidden === false) {
            location.reload();
        }
    }, 30000);
    
    // Add relative time formatting
    document.addEventListener('DOMContentLoaded', function() {
        // Using server-side timestamp formatting
        console.log('Data collection page loaded');
    });
</script>
{% endblock %}
