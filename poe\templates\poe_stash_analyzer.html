{% extends "poe_base.html" %}

{% block title %}Stash Analyzer - POE Currency Analyzer{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-archive"></i> Stash Analyzer</h1>
        {% if account %}
        <p class="text-muted">Analyze your stash tabs for valuable items and sell recommendations</p>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i> Connected to account: <strong>{{ account.account_name }}</strong>
        </div>
        {% else %}
        <p class="text-muted">Connect your POE account to analyze your stash tabs</p>
        {% endif %}
    </div>
</div>

{% if error %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle"></i> {{ error }}
        </div>
    </div>
</div>
{% endif %}

{% if account %}
<!-- Stash Tab Selection -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-folder"></i> Select Stash Tab</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="leagueSelect" class="form-label">League</label>
                    <select class="form-select" id="leagueSelect">
                        <option value="Mercenaries">Mercenaries</option>
                        <option value="Standard">Standard</option>
                        <option value="Hardcore">Hardcore</option>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label for="stashTabSelect" class="form-label">Stash Tab</label>
                    <select class="form-select" id="stashTabSelect" disabled>
                        <option value="">Loading stash tabs...</option>
                    </select>
                </div>
                
                <button class="btn btn-primary" id="analyzeBtn" onclick="analyzeStashTab()" disabled>
                    <i class="fas fa-search"></i> Analyze Stash Tab
                </button>
                
                <button class="btn btn-outline-secondary" onclick="loadStashTabs()">
                    <i class="fas fa-sync-alt"></i> Refresh Tabs
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Analysis Info</h5>
            </div>
            <div class="card-body">
                <div id="analysisInfo">
                    <p class="text-muted">Select a stash tab to see analysis information.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Analysis Results -->
<div class="row mb-4" id="analysisResults" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-chart-pie"></i> Stash Analysis Results</h5>
                <span id="analysisTimestamp" class="text-muted small"></span>
            </div>
            <div class="card-body">
                <!-- Value Summary -->
                <div class="row mb-4" id="valueSummary">
                    <!-- Will be populated by JavaScript -->
                </div>
                
                <!-- Top Items -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6><i class="fas fa-crown"></i> Most Valuable Items</h6>
                        <div class="table-responsive">
                            <table class="table table-dark table-sm" id="topItemsTable">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Value</th>
                                        <th>Confidence</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="fas fa-tags"></i> Sell Recommendations</h6>
                        <div class="table-responsive">
                            <table class="table table-dark table-sm" id="sellRecommendationsTable">
                                <thead>
                                    <tr>
                                        <th>Item</th>
                                        <th>Value</th>
                                        <th>Priority</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <!-- Category Breakdown -->
                <div class="row">
                    <div class="col-12">
                        <h6><i class="fas fa-chart-bar"></i> Value by Category</h6>
                        <div id="categoryBreakdown">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- Account Not Linked -->
<div class="row">
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-unlink fa-4x text-muted mb-3"></i>
            <h4>Account Not Linked</h4>
            <p class="text-muted mb-4">
                To analyze your stash tabs, you need to link your Path of Exile account first.
            </p>
            <a href="/stash" class="btn btn-primary">
                <i class="fas fa-link"></i> Link POE Account
            </a>
        </div>
    </div>
</div>
{% endif %}

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>Analyzing Stash Tab</h5>
                <p class="text-muted">This may take a few moments...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentStashTabs = [];
let currentAnalysis = null;

// Load stash tabs on page load
document.addEventListener('DOMContentLoaded', function() {
    {% if account %}
    loadStashTabs();
    {% endif %}
});

// League selection change
document.getElementById('leagueSelect').addEventListener('change', function() {
    loadStashTabs();
});

function loadStashTabs() {
    const league = document.getElementById('leagueSelect').value;
    const stashSelect = document.getElementById('stashTabSelect');
    
    stashSelect.innerHTML = '<option value="">Loading stash tabs...</option>';
    stashSelect.disabled = true;
    document.getElementById('analyzeBtn').disabled = true;
    
    fetch(`/api/stash/tabs?league=${league}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error loading stash tabs: ' + data.error);
            return;
        }
        
        currentStashTabs = data.stash_tabs;
        
        // Populate stash tab dropdown
        stashSelect.innerHTML = '<option value="">Select a stash tab...</option>';
        
        data.stash_tabs.forEach(tab => {
            const option = document.createElement('option');
            option.value = tab.id;
            option.textContent = `${tab.name} (${tab.type})`;
            stashSelect.appendChild(option);
        });
        
        stashSelect.disabled = false;
        
        // Enable analyze button when tab is selected
        stashSelect.addEventListener('change', function() {
            document.getElementById('analyzeBtn').disabled = !this.value;
        });
    })
    .catch(error => {
        console.error('Error loading stash tabs:', error);
        alert('Failed to load stash tabs');
    });
}

function analyzeStashTab() {
    const stashId = document.getElementById('stashTabSelect').value;
    const league = document.getElementById('leagueSelect').value;
    
    if (!stashId) {
        alert('Please select a stash tab');
        return;
    }
    
    // Show loading modal
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    fetch(`/api/stash/analyze/${stashId}?league=${league}`)
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        
        if (data.error) {
            alert('Error analyzing stash tab: ' + data.error);
            return;
        }
        
        currentAnalysis = data;
        displayAnalysisResults(data);
    })
    .catch(error => {
        loadingModal.hide();
        console.error('Error analyzing stash tab:', error);
        alert('Failed to analyze stash tab');
    });
}

function displayAnalysisResults(data) {
    // Show results section
    document.getElementById('analysisResults').style.display = 'block';
    
    // Update timestamp
    document.getElementById('analysisTimestamp').textContent = 
        'Analyzed: ' + new Date().toLocaleString();
    
    // Display value summary
    displayValueSummary(data.value_analysis);
    
    // Display top items
    displayTopItems(data.top_items);
    
    // Display sell recommendations
    displaySellRecommendations(data.sell_recommendations);
    
    // Display category breakdown
    displayCategoryBreakdown(data.value_analysis.category_breakdown);
    
    // Scroll to results
    document.getElementById('analysisResults').scrollIntoView({ behavior: 'smooth' });
}

function displayValueSummary(valueAnalysis) {
    const summaryDiv = document.getElementById('valueSummary');
    
    const html = `
        <div class="col-md-3 text-center">
            <h6>Total Value</h6>
            <h4 class="currency-value">${valueAnalysis.total_value.toFixed(2)}</h4>
            <small class="text-muted">Chaos Orbs</small>
        </div>
        <div class="col-md-3 text-center">
            <h6>Currency Value</h6>
            <h4 class="text-warning">${valueAnalysis.currency_value.toFixed(2)}</h4>
            <small class="text-muted">Chaos Orbs</small>
        </div>
        <div class="col-md-3 text-center">
            <h6>Equipment Value</h6>
            <h4 class="text-info">${valueAnalysis.equipment_value.toFixed(2)}</h4>
            <small class="text-muted">Chaos Orbs</small>
        </div>
        <div class="col-md-3 text-center">
            <h6>Items</h6>
            <h4 class="text-success">${valueAnalysis.item_count}</h4>
            <small class="text-muted">${valueAnalysis.valuable_item_count} valuable</small>
        </div>
    `;
    
    summaryDiv.innerHTML = html;
}

function displayTopItems(topItems) {
    const tbody = document.querySelector('#topItemsTable tbody');
    tbody.innerHTML = '';
    
    topItems.slice(0, 10).forEach(item => {
        const row = document.createElement('tr');
        
        const confidenceClass = item.confidence === 'high' ? 'text-success' : 
                               item.confidence === 'medium' ? 'text-warning' : 'text-danger';
        
        row.innerHTML = `
            <td>${item.item_name}</td>
            <td class="currency-value">${item.estimated_value.toFixed(2)}</td>
            <td><span class="badge ${confidenceClass}">${item.confidence}</span></td>
        `;
        
        tbody.appendChild(row);
    });
}

function displaySellRecommendations(recommendations) {
    const tbody = document.querySelector('#sellRecommendationsTable tbody');
    tbody.innerHTML = '';
    
    recommendations.slice(0, 10).forEach(rec => {
        const row = document.createElement('tr');
        
        const priorityClass = rec.sell_priority >= 10 ? 'text-success' : 
                             rec.sell_priority >= 5 ? 'text-warning' : 'text-info';
        
        row.innerHTML = `
            <td title="${rec.reason}">${rec.item_name}</td>
            <td class="currency-value">${rec.estimated_value.toFixed(2)}</td>
            <td><span class="badge ${priorityClass}">${rec.sell_priority.toFixed(1)}</span></td>
        `;
        
        tbody.appendChild(row);
    });
}

function displayCategoryBreakdown(categoryBreakdown) {
    const breakdownDiv = document.getElementById('categoryBreakdown');
    
    let html = '<div class="row">';
    
    Object.entries(categoryBreakdown).forEach(([category, value]) => {
        if (value > 0) {
            html += `
                <div class="col-md-3 mb-2">
                    <div class="d-flex justify-content-between">
                        <span>${category}:</span>
                        <span class="currency-value">${value.toFixed(2)} chaos</span>
                    </div>
                </div>
            `;
        }
    });
    
    html += '</div>';
    breakdownDiv.innerHTML = html;
}
</script>
{% endblock %}
