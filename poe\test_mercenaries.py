from poe_get_currency_prices import POENinjaAPI

# Test Mercenaries league (capitalized)
api = POENinjaAPI(league="Mercenaries")
data = api.get_currency_data('currency')

if data and 'lines' in data:
    print(f"Mercenaries league has {len(data['lines'])} currencies")
    if data['lines']:
        print("Sample currencies:")
        for i, curr in enumerate(data['lines'][:3]):
            name = curr.get('currencyTypeName', 'Unknown')
            value = curr.get('chaosEquivalent', 0)
            print(f"  {i+1}. {name}: {value} chaos")
    else:
        print("No currencies found in Mercenaries league")
else:
    print("No data available for Mercenaries league")
