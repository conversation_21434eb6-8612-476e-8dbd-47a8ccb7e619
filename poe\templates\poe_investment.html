{% extends "poe_base.html" %}

{% block title %}POE Investment Calculator{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1><i class="fas fa-calculator"></i> Investment Calculator</h1>
        <p class="text-muted">Calculate investment scenarios and manage your currency portfolio</p>
    </div>
</div>

<!-- Investment Calculator Section -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-coins"></i> Investment Scenario Calculator</h5>
            </div>
            <div class="card-body">
                <form id="investmentForm">
                    <div class="mb-3">
                        <label for="investmentAmount" class="form-label">Investment Amount (Chaos Orbs)</label>
                        <input type="number" class="form-control" id="investmentAmount" 
                               placeholder="Enter amount in chaos orbs" min="1" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="targetCurrency" class="form-label">Target Currency</label>
                        <select class="form-select" id="targetCurrency" required>
                            <option value="">Select a currency...</option>
                            {% for currency in currencies %}
                            <option value="{{ currency.name }}" data-price="{{ currency.chaos_equivalent }}">
                                {{ currency.name }} ({{ "%.2f"|format(currency.chaos_equivalent) }} chaos)
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-calculator"></i> Calculate Scenario
                    </button>
                </form>
                
                <!-- Scenario Results -->
                <div id="scenarioResults" class="mt-4" style="display: none;">
                    <h6>Investment Scenario Results</h6>
                    <div id="scenarioContent"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-plus-circle"></i> Create Investment</h5>
            </div>
            <div class="card-body">
                <form id="createInvestmentForm">
                    <div class="mb-3">
                        <label for="investCurrency" class="form-label">Currency</label>
                        <select class="form-select" id="investCurrency" required>
                            <option value="">Select a currency...</option>
                            {% for currency in currencies %}
                            <option value="{{ currency.name }}">{{ currency.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="investAmount" class="form-label">Investment Amount (Chaos Orbs)</label>
                        <input type="number" class="form-control" id="investAmount" 
                               placeholder="Amount to invest" min="1" step="0.01" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="targetPrice" class="form-label">Target Sell Price (Optional)</label>
                        <input type="number" class="form-control" id="targetPrice" 
                               placeholder="Target price per unit" min="0" step="0.01">
                    </div>
                    
                    <div class="mb-3">
                        <label for="stopLoss" class="form-label">Stop Loss Price (Optional)</label>
                        <input type="number" class="form-control" id="stopLoss" 
                               placeholder="Stop loss price per unit" min="0" step="0.01">
                    </div>
                    
                    <div class="mb-3">
                        <label for="investmentNotes" class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="investmentNotes" rows="2" 
                                  placeholder="Investment notes or strategy"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-plus"></i> Create Investment
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Portfolio Performance Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-briefcase"></i> Portfolio Performance</h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshPortfolio()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                {% if portfolio.get('error') %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> {{ portfolio.error }}
                </div>
                {% elif portfolio.get('total_investments', 0) > 0 %}
                <!-- Portfolio Summary -->
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>Total Invested</h6>
                            <h4 class="currency-value">{{ "%.2f"|format(portfolio.total_invested) }}</h4>
                            <small class="text-muted">Chaos Orbs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>Current Value</h6>
                            <h4 class="currency-value">{{ "%.2f"|format(portfolio.total_current_value) }}</h4>
                            <small class="text-muted">Chaos Orbs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>Total P&L</h6>
                            <h4 class="{% if portfolio.total_profit_loss >= 0 %}price-up{% else %}price-down{% endif %}">
                                {{ "%.2f"|format(portfolio.total_profit_loss) }}
                            </h4>
                            <small class="text-muted">Chaos Orbs</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6>Portfolio Return</h6>
                            <h4 class="{% if portfolio.portfolio_return_percentage >= 0 %}price-up{% else %}price-down{% endif %}">
                                {{ "%.1f"|format(portfolio.portfolio_return_percentage) }}%
                            </h4>
                            <small class="text-muted">Win Rate: {{ "%.1f"|format(portfolio.win_rate) }}%</small>
                        </div>
                    </div>
                </div>
                
                <!-- Individual Investments -->
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th>Invested</th>
                                <th>Quantity</th>
                                <th>Buy Price</th>
                                <th>Current Price</th>
                                <th>Current Value</th>
                                <th>P&L</th>
                                <th>Return %</th>
                                <th>Days Held</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for investment in portfolio.investments %}
                            <tr>
                                <td>{{ investment.investment.currency_name }}</td>
                                <td>{{ "%.2f"|format(investment.investment.amount_invested) }}</td>
                                <td>{{ "%.4f"|format(investment.investment.quantity_bought) }}</td>
                                <td>{{ "%.2f"|format(investment.investment.buy_price) }}</td>
                                <td>{{ "%.2f"|format(investment.current_price) }}</td>
                                <td>{{ "%.2f"|format(investment.current_value) }}</td>
                                <td class="{% if investment.profit_loss >= 0 %}price-up{% else %}price-down{% endif %}">
                                    {{ "%.2f"|format(investment.profit_loss) }}
                                </td>
                                <td class="{% if investment.profit_loss_percentage >= 0 %}price-up{% else %}price-down{% endif %}">
                                    {{ "%.1f"|format(investment.profit_loss_percentage) }}%
                                </td>
                                <td>{{ investment.days_held }}</td>
                                <td>
                                    <span class="badge {% if investment.status == 'profit' %}bg-success{% elif investment.status == 'loss' %}bg-danger{% else %}bg-secondary{% endif %}">
                                        {{ investment.status.title() }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No investments in portfolio yet. Create your first investment above!</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Investment Opportunities Section -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-search"></i> Investment Opportunities</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="opportunityBudget" class="form-label">Budget (Chaos Orbs)</label>
                        <input type="number" class="form-control" id="opportunityBudget" 
                               value="100" min="1" step="1">
                    </div>
                    <div class="col-md-4">
                        <label for="minReturn" class="form-label">Min Expected Return (%)</label>
                        <input type="number" class="form-control" id="minReturn" 
                               value="10" min="0" step="1">
                    </div>
                    <div class="col-md-4">
                        <label for="maxRisk" class="form-label">Max Risk Level</label>
                        <select class="form-select" id="maxRisk">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                </div>
                
                <button class="btn btn-info" onclick="findOpportunities()">
                    <i class="fas fa-search"></i> Find Opportunities
                </button>
                
                <div id="opportunitiesResults" class="mt-4" style="display: none;">
                    <h6>Investment Opportunities</h6>
                    <div id="opportunitiesContent"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Investment scenario calculation
document.getElementById('investmentForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const amount = document.getElementById('investmentAmount').value;
    const currency = document.getElementById('targetCurrency').value;

    if (!amount || !currency) {
        alert('Please fill in all required fields');
        return;
    }

    fetch('/api/investment/scenario', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            investment_amount: parseFloat(amount),
            target_currency: currency
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
            return;
        }

        displayScenarioResults(data);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to calculate scenario');
    });
});

// Create investment
document.getElementById('createInvestmentForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const currency = document.getElementById('investCurrency').value;
    const amount = document.getElementById('investAmount').value;
    const targetPrice = document.getElementById('targetPrice').value;
    const stopLoss = document.getElementById('stopLoss').value;
    const notes = document.getElementById('investmentNotes').value;

    if (!currency || !amount) {
        alert('Please fill in all required fields');
        return;
    }

    const investmentData = {
        currency_name: currency,
        amount_invested: parseFloat(amount),
        notes: notes
    };

    if (targetPrice) investmentData.target_sell_price = parseFloat(targetPrice);
    if (stopLoss) investmentData.stop_loss_price = parseFloat(stopLoss);

    fetch('/api/investment/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(investmentData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
            return;
        }

        alert('Investment created successfully!');
        document.getElementById('createInvestmentForm').reset();
        location.reload(); // Refresh to show updated portfolio
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to create investment');
    });
});

function displayScenarioResults(data) {
    const resultsDiv = document.getElementById('scenarioResults');
    const contentDiv = document.getElementById('scenarioContent');

    let html = `
        <div class="alert alert-info">
            <strong>${data.currency_name}</strong> Investment Scenario
        </div>

        <div class="row">
            <div class="col-md-6">
                <h6>Investment Details</h6>
                <ul class="list-unstyled">
                    <li><strong>Investment Amount:</strong> ${data.investment_amount.toFixed(2)} chaos</li>
                    <li><strong>Current Price:</strong> ${data.current_price.toFixed(2)} chaos</li>
                    <li><strong>Quantity Buyable:</strong> ${data.quantity_buyable.toFixed(4)} units</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Price Statistics</h6>
                <ul class="list-unstyled">
                    <li><strong>30-day High:</strong> ${data.max_price ? data.max_price.toFixed(2) : 'N/A'} chaos</li>
                    <li><strong>30-day Low:</strong> ${data.min_price ? data.min_price.toFixed(2) : 'N/A'} chaos</li>
                    <li><strong>Average Price:</strong> ${data.avg_price ? data.avg_price.toFixed(2) : 'N/A'} chaos</li>
                    <li><strong>Volatility:</strong> ${data.volatility ? data.volatility.toFixed(2) : 'N/A'}</li>
                </ul>
            </div>
        </div>
    `;

    if (data.scenarios) {
        html += `
            <h6>Profit Scenarios</h6>
            <div class="table-responsive">
                <table class="table table-sm table-dark">
                    <thead>
                        <tr>
                            <th>Scenario</th>
                            <th>Target Price</th>
                            <th>Expected Value</th>
                            <th>Expected Profit</th>
                            <th>Return %</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        for (const [scenario, details] of Object.entries(data.scenarios)) {
            const returnClass = details.expected_return >= 0 ? 'price-up' : 'price-down';
            html += `
                <tr>
                    <td>${scenario.charAt(0).toUpperCase() + scenario.slice(1)}</td>
                    <td>${details.price_target.toFixed(2)}</td>
                    <td>${details.expected_value.toFixed(2)}</td>
                    <td class="${returnClass}">${details.expected_profit.toFixed(2)}</td>
                    <td class="${returnClass}">${details.expected_return.toFixed(1)}%</td>
                </tr>
            `;
        }

        html += `
                    </tbody>
                </table>
            </div>
        `;
    }

    contentDiv.innerHTML = html;
    resultsDiv.style.display = 'block';
}

function refreshPortfolio() {
    location.reload();
}

function findOpportunities() {
    const budget = document.getElementById('opportunityBudget').value;
    const minReturn = document.getElementById('minReturn').value;
    const maxRisk = document.getElementById('maxRisk').value;

    const params = new URLSearchParams({
        budget: budget,
        min_return: minReturn,
        max_risk: maxRisk
    });

    fetch(`/api/investment/opportunities?${params}`)
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert('Error: ' + data.error);
            return;
        }

        displayOpportunities(data.opportunities);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to find opportunities');
    });
}

function displayOpportunities(opportunities) {
    const resultsDiv = document.getElementById('opportunitiesResults');
    const contentDiv = document.getElementById('opportunitiesContent');

    if (!opportunities || opportunities.length === 0) {
        contentDiv.innerHTML = '<div class="alert alert-warning">No opportunities found with current criteria.</div>';
        resultsDiv.style.display = 'block';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-dark table-striped">
                <thead>
                    <tr>
                        <th>Currency</th>
                        <th>Current Price</th>
                        <th>Quantity Buyable</th>
                        <th>Expected Return</th>
                        <th>Risk Level</th>
                        <th>Recommendation</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
    `;

    opportunities.forEach(opp => {
        const returnClass = opp.expected_return >= 0 ? 'price-up' : 'price-down';
        const riskBadge = opp.risk_level === 'low' ? 'bg-success' :
                         opp.risk_level === 'medium' ? 'bg-warning' : 'bg-danger';

        html += `
            <tr>
                <td>${opp.currency_name}</td>
                <td>${opp.current_price.toFixed(2)}</td>
                <td>${opp.quantity_buyable.toFixed(4)}</td>
                <td class="${returnClass}">${opp.expected_return.toFixed(1)}%</td>
                <td><span class="badge ${riskBadge}">${opp.risk_level}</span></td>
                <td><span class="badge ${opp.recommendation === 'BUY' ? 'bg-success' : 'bg-secondary'}">${opp.recommendation}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="quickInvest('${opp.currency_name}', ${opp.investment_amount})">
                        Quick Invest
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    contentDiv.innerHTML = html;
    resultsDiv.style.display = 'block';
}

function quickInvest(currencyName, amount) {
    document.getElementById('investCurrency').value = currencyName;
    document.getElementById('investAmount').value = amount;

    // Scroll to create investment form
    document.getElementById('createInvestmentForm').scrollIntoView({ behavior: 'smooth' });
}
</script>
{% endblock %}
