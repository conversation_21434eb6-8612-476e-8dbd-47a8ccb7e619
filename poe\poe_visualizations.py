"""
POE Currency Visualization Demo

This script demonstrates the visualization capabilities of the analysis tool.
"""

import os
from poe_get_currency_prices import CurrencyDataManager
from poe_currency_analyzer import CurrencyAnalyzer


def create_sample_charts():
    """Create sample charts and save them as HTML files"""
    print("📊 CREATING VISUALIZATION DEMOS")
    print("=" * 50)
    
    data_manager = CurrencyDataManager()
    analyzer = CurrencyAnalyzer(data_manager)
    
    # Get historical data
    df = analyzer.get_historical_data(days=30)
    
    if df.empty:
        print("❌ No data available for visualization")
        print("   Run the data collector first to gather some data")
        return
    
    print(f"✓ Loaded {len(df)} data points for visualization")
    
    # Create output directory for charts
    charts_dir = "poe_charts"
    if not os.path.exists(charts_dir):
        os.makedirs(charts_dir)
        print(f"✓ Created charts directory: {charts_dir}")
    
    # 1. Market Overview Chart
    print("\n1. Creating market overview chart...")
    try:
        market_fig = analyzer.create_market_overview_chart(df)
        market_path = os.path.join(charts_dir, "market_overview.html")
        market_fig.write_html(market_path)
        print(f"   ✓ Saved to: {market_path}")
    except Exception as e:
        print(f"   ❌ Failed to create market overview: {e}")
    
    # 2. Individual Currency Charts (for top currencies)
    print("\n2. Creating individual currency trend charts...")
    
    # Get top valuable currencies
    latest_data = df.groupby('name').last().reset_index()
    top_currencies = latest_data.nlargest(5, 'chaos_equivalent')
    
    for _, currency in top_currencies.iterrows():
        currency_name = currency['name']
        safe_name = "".join(c for c in currency_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        
        try:
            currency_fig = analyzer.create_price_trend_chart(currency_name, df)
            currency_path = os.path.join(charts_dir, f"trend_{safe_name.replace(' ', '_')}.html")
            currency_fig.write_html(currency_path)
            print(f"   ✓ {currency_name}: {currency_path}")
        except Exception as e:
            print(f"   ❌ Failed to create chart for {currency_name}: {e}")
    
    # 3. Create a summary HTML page
    print("\n3. Creating summary dashboard...")
    create_dashboard_html(charts_dir, top_currencies)
    
    print(f"\n✅ Visualization demo completed!")
    print(f"   Open {os.path.join(charts_dir, 'dashboard.html')} in your browser to view the results")


def create_dashboard_html(charts_dir, top_currencies):
    """Create a simple HTML dashboard that links to all charts"""
    
    dashboard_html = """
<!DOCTYPE html>
<html>
<head>
    <title>POE Currency Analysis Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        h2 { color: #666; border-bottom: 2px solid #eee; padding-bottom: 10px; }
        .chart-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .chart-card { background: #f9f9f9; padding: 15px; border-radius: 5px; border: 1px solid #ddd; }
        .chart-card h3 { margin-top: 0; color: #444; }
        .chart-link { display: inline-block; background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-top: 10px; }
        .chart-link:hover { background: #0056b3; }
        .stats { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Path of Exile Currency Analysis Dashboard</h1>
        <p style="text-align: center; color: #666;">Your private currency price analysis tool - Similar to poe.ninja but for personal analytics</p>
        
        <div class="stats">
            <h3>📊 Quick Stats</h3>
            <p><strong>Top Currencies Tracked:</strong> """ + str(len(top_currencies)) + """</p>
            <p><strong>Analysis Date:</strong> """ + str(top_currencies.iloc[0]['timestamp'] if not top_currencies.empty else 'N/A') + """</p>
        </div>
        
        <h2>📈 Market Overview</h2>
        <div class="chart-card">
            <h3>Overall Market Snapshot</h3>
            <p>View the top 20 currencies by value in the current market.</p>
            <a href="market_overview.html" class="chart-link" target="_blank">View Market Overview</a>
        </div>
        
        <h2>💰 Top Currency Trends</h2>
        <div class="chart-grid">
"""
    
    # Add cards for each top currency
    for _, currency in top_currencies.iterrows():
        currency_name = currency['name']
        safe_name = "".join(c for c in currency_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        value = currency['chaos_equivalent']
        
        dashboard_html += f"""
            <div class="chart-card">
                <h3>{currency_name}</h3>
                <p><strong>Current Value:</strong> {value:,.2f} chaos</p>
                <p>View detailed price trend and historical data for this currency.</p>
                <a href="trend_{safe_name.replace(' ', '_')}.html" class="chart-link" target="_blank">View Trend</a>
            </div>
        """
    
    dashboard_html += """
        </div>
        
        <h2>🔧 Tools & Features</h2>
        <div class="chart-grid">
            <div class="chart-card">
                <h3>Data Collection</h3>
                <p>Automated data collection from poe.ninja API with configurable intervals.</p>
                <code>python poe_data_collector.py</code>
            </div>
            <div class="chart-card">
                <h3>Analysis Engine</h3>
                <p>Advanced analysis including trend detection, profit opportunities, and market insights.</p>
                <code>python poe_demo.py</code>
            </div>
            <div class="chart-card">
                <h3>Export & Reports</h3>
                <p>Generate detailed JSON reports and export data for further analysis.</p>
                <code>python test_analyzer.py</code>
            </div>
        </div>
        
        <div class="footer">
            <p>🛠️ Built with Python, Pandas, and Plotly | 📊 Data from poe.ninja API</p>
            <p>This tool is not affiliated with or endorsed by Grinding Gear Games.</p>
        </div>
    </div>
</body>
</html>
"""
    
    dashboard_path = os.path.join(charts_dir, "dashboard.html")
    with open(dashboard_path, 'w', encoding='utf-8') as f:
        f.write(dashboard_html)
    
    print(f"   ✓ Dashboard saved to: {dashboard_path}")


def main():
    """Main function for visualization demo"""
    print("🎨 POE CURRENCY VISUALIZATION DEMO")
    print("=" * 50)
    print("This will create interactive charts and a dashboard for your currency data.")
    print()
    
    try:
        create_sample_charts()
    except Exception as e:
        print(f"❌ Visualization demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
