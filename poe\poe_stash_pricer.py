"""
POE Stash Price Analysis Module

This module analyzes stash items and provides market price estimates
using poe.ninja API and other pricing sources.
"""

import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging
from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager
from poe_stash_scanner import StashItem

logger = logging.getLogger(__name__)


@dataclass
class ItemPrice:
    """Represents price information for an item"""
    item_name: str
    base_type: str
    estimated_value: float  # In chaos orbs
    confidence: str  # high, medium, low
    price_source: str  # poe.ninja, player_note, estimated
    market_data: Dict
    last_updated: datetime
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return {
            'item_name': self.item_name,
            'base_type': self.base_type,
            'estimated_value': self.estimated_value,
            'confidence': self.confidence,
            'price_source': self.price_source,
            'market_data': self.market_data,
            'last_updated': self.last_updated.isoformat()
        }


@dataclass
class StashValue:
    """Represents total value analysis of stash"""
    total_value: float
    currency_value: float
    equipment_value: float
    other_value: float
    item_count: int
    valuable_item_count: int
    top_items: List[ItemPrice]
    category_breakdown: Dict[str, float]
    analysis_timestamp: datetime


class StashPricer:
    """Price analyzer for stash items"""
    
    def __init__(self, league: str = "Mercenaries"):
        """
        Initialize stash pricer
        
        Args:
            league: League to get prices for
        """
        self.league = league
        self.poe_api = POENinjaAPI(league=league)
        self.data_manager = CurrencyDataManager()
        self.currency_prices = {}
        self.unique_prices = {}
        self.gem_prices = {}
        self.map_prices = {}
        self._load_market_data()
    
    def _load_market_data(self):
        """Load current market data from poe.ninja"""
        try:
            # Load currency prices
            currency_data = self.poe_api.get_currency_data('currency')
            if currency_data:
                df = self.data_manager.convert_to_dataframe(currency_data, 'currency')
                for _, row in df.iterrows():
                    self.currency_prices[row['name']] = row['chaos_equivalent']
            
            # Load fragment prices
            fragment_data = self.poe_api.get_currency_data('fragment')
            if fragment_data:
                df = self.data_manager.convert_to_dataframe(fragment_data, 'fragment')
                for _, row in df.iterrows():
                    self.currency_prices[row['name']] = row['chaos_equivalent']
            
            # Load unique item prices
            unique_data = self.poe_api.get_item_data('unique_weapon')
            if unique_data:
                self._process_unique_data(unique_data)
                
            unique_armour = self.poe_api.get_item_data('unique_armour')
            if unique_armour:
                self._process_unique_data(unique_armour)
                
            unique_accessory = self.poe_api.get_item_data('unique_accessory')
            if unique_accessory:
                self._process_unique_data(unique_accessory)
            
            # Load gem prices
            gem_data = self.poe_api.get_item_data('skill_gem')
            if gem_data:
                self._process_gem_data(gem_data)
            
            # Load map prices
            map_data = self.poe_api.get_item_data('map')
            if map_data:
                self._process_map_data(map_data)
                
            logger.info(f"Loaded market data: {len(self.currency_prices)} currencies, "
                       f"{len(self.unique_prices)} uniques, {len(self.gem_prices)} gems")
                       
        except Exception as e:
            logger.error(f"Error loading market data: {e}")
    
    def _process_unique_data(self, unique_data: Dict):
        """Process unique item data from poe.ninja"""
        if 'lines' not in unique_data:
            return
            
        for item in unique_data['lines']:
            name = item.get('name', '')
            base_type = item.get('baseType', '')
            chaos_value = item.get('chaosValue', 0)
            
            if name and chaos_value > 0:
                # Store both by name and by base type
                self.unique_prices[name] = chaos_value
                if base_type:
                    key = f"{name} {base_type}"
                    self.unique_prices[key] = chaos_value
    
    def _process_gem_data(self, gem_data: Dict):
        """Process gem data from poe.ninja"""
        if 'lines' not in gem_data:
            return
            
        for gem in gem_data['lines']:
            name = gem.get('name', '')
            chaos_value = gem.get('chaosValue', 0)
            gem_level = gem.get('gemLevel', 1)
            gem_quality = gem.get('gemQuality', 0)
            corrupted = gem.get('corrupted', False)
            
            if name and chaos_value > 0:
                # Create key with level and quality
                key = f"{name} L{gem_level}"
                if gem_quality > 0:
                    key += f" Q{gem_quality}"
                if corrupted:
                    key += " (Corrupted)"
                    
                self.gem_prices[key] = chaos_value
                # Also store base name
                self.gem_prices[name] = max(self.gem_prices.get(name, 0), chaos_value)
    
    def _process_map_data(self, map_data: Dict):
        """Process map data from poe.ninja"""
        if 'lines' not in map_data:
            return
            
        for map_item in map_data['lines']:
            name = map_item.get('name', '')
            base_type = map_item.get('baseType', '')
            chaos_value = map_item.get('chaosValue', 0)
            map_tier = map_item.get('mapTier', 0)
            
            if name and chaos_value > 0:
                key = f"{base_type} T{map_tier}" if base_type else name
                self.map_prices[key] = chaos_value
    
    def price_item(self, item: StashItem) -> ItemPrice:
        """
        Get price estimate for a single item
        
        Args:
            item: StashItem to price
            
        Returns:
            ItemPrice with estimate
        """
        # Check if item has player note with price
        player_price = self._parse_player_note(item.note)
        if player_price:
            return ItemPrice(
                item_name=item.name,
                base_type=item.base_type,
                estimated_value=player_price,
                confidence="high",
                price_source="player_note",
                market_data={'note': item.note},
                last_updated=datetime.now()
            )
        
        # Price based on category
        if item.category == "Currency":
            return self._price_currency(item)
        elif item.category in ["Weapons", "Armour", "Accessories"] and item.rarity == "Unique":
            return self._price_unique_item(item)
        elif item.category == "Gems":
            return self._price_gem(item)
        elif item.category == "Maps":
            return self._price_map(item)
        elif item.category in ["Fragments", "Divination", "Essences", "Fossils", "Scarabs"]:
            return self._price_currency_like(item)
        else:
            return self._estimate_generic_item(item)
    
    def _parse_player_note(self, note: str) -> Optional[float]:
        """Parse player note for price information"""
        if not note:
            return None
            
        # Common price patterns
        patterns = [
            r'~price (\d+(?:\.\d+)?)\s*chaos',
            r'~b/o (\d+(?:\.\d+)?)\s*chaos',
            r'(\d+(?:\.\d+)?)\s*c\b',
            r'(\d+(?:\.\d+)?)\s*chaos',
        ]
        
        note_lower = note.lower()
        for pattern in patterns:
            match = re.search(pattern, note_lower)
            if match:
                try:
                    return float(match.group(1))
                except ValueError:
                    continue
                    
        return None
    
    def _price_currency(self, item: StashItem) -> ItemPrice:
        """Price currency items"""
        price_per_unit = self.currency_prices.get(item.name, 0)
        total_value = price_per_unit * item.stack_size
        
        confidence = "high" if price_per_unit > 0 else "low"
        
        return ItemPrice(
            item_name=item.name,
            base_type=item.base_type,
            estimated_value=total_value,
            confidence=confidence,
            price_source="poe.ninja",
            market_data={'price_per_unit': price_per_unit, 'stack_size': item.stack_size},
            last_updated=datetime.now()
        )
    
    def _price_unique_item(self, item: StashItem) -> ItemPrice:
        """Price unique items"""
        # Try different name combinations
        possible_names = [
            item.name,
            item.base_type,
            f"{item.name} {item.base_type}".strip()
        ]
        
        price = 0
        for name in possible_names:
            if name in self.unique_prices:
                price = self.unique_prices[name]
                break
        
        confidence = "high" if price > 0 else "medium"
        
        return ItemPrice(
            item_name=item.name,
            base_type=item.base_type,
            estimated_value=price,
            confidence=confidence,
            price_source="poe.ninja" if price > 0 else "estimated",
            market_data={'unique_price': price},
            last_updated=datetime.now()
        )
    
    def _price_gem(self, item: StashItem) -> ItemPrice:
        """Price gems"""
        # Extract gem level and quality from properties
        gem_level = 1
        gem_quality = 0
        
        if 'Level' in item.properties:
            try:
                gem_level = int(item.properties['Level'])
            except ValueError:
                pass
                
        if 'Quality' in item.properties:
            try:
                quality_str = item.properties['Quality'].replace('%', '')
                gem_quality = int(quality_str)
            except ValueError:
                pass
        
        # Try to find price with level/quality
        possible_keys = [
            f"{item.name} L{gem_level} Q{gem_quality}",
            f"{item.name} L{gem_level}",
            item.name
        ]
        
        price = 0
        for key in possible_keys:
            if key in self.gem_prices:
                price = self.gem_prices[key]
                break
        
        confidence = "medium" if price > 0 else "low"
        
        return ItemPrice(
            item_name=item.name,
            base_type=item.base_type,
            estimated_value=price,
            confidence=confidence,
            price_source="poe.ninja" if price > 0 else "estimated",
            market_data={'gem_level': gem_level, 'gem_quality': gem_quality},
            last_updated=datetime.now()
        )
    
    def _price_map(self, item: StashItem) -> ItemPrice:
        """Price maps"""
        # Extract map tier
        map_tier = 1
        if 'Map Tier' in item.properties:
            try:
                map_tier = int(item.properties['Map Tier'])
            except ValueError:
                pass
        
        map_key = f"{item.base_type} T{map_tier}"
        price = self.map_prices.get(map_key, 0)
        
        if price == 0:
            price = self.map_prices.get(item.name, 0)
        
        confidence = "medium" if price > 0 else "low"
        
        return ItemPrice(
            item_name=item.name,
            base_type=item.base_type,
            estimated_value=price,
            confidence=confidence,
            price_source="poe.ninja" if price > 0 else "estimated",
            market_data={'map_tier': map_tier},
            last_updated=datetime.now()
        )
    
    def _price_currency_like(self, item: StashItem) -> ItemPrice:
        """Price currency-like items (fragments, cards, etc.)"""
        price_per_unit = self.currency_prices.get(item.name, 0)
        total_value = price_per_unit * item.stack_size
        
        confidence = "high" if price_per_unit > 0 else "low"
        
        return ItemPrice(
            item_name=item.name,
            base_type=item.base_type,
            estimated_value=total_value,
            confidence=confidence,
            price_source="poe.ninja" if price_per_unit > 0 else "estimated",
            market_data={'price_per_unit': price_per_unit, 'stack_size': item.stack_size},
            last_updated=datetime.now()
        )
    
    def _estimate_generic_item(self, item: StashItem) -> ItemPrice:
        """Estimate price for generic items"""
        # Basic estimation based on item properties
        estimated_value = 0
        
        # High level items might be valuable
        if item.item_level >= 85:
            estimated_value = 1
        elif item.item_level >= 80:
            estimated_value = 0.5
        
        # Items with influences
        if item.influences:
            estimated_value += len(item.influences) * 2
        
        # 6-link items
        if item._has_six_links():
            estimated_value += 10
        
        # Synthesised/fractured items
        if item.synthesised or item.fractured:
            estimated_value += 1
        
        return ItemPrice(
            item_name=item.name,
            base_type=item.base_type,
            estimated_value=estimated_value,
            confidence="low",
            price_source="estimated",
            market_data={'estimation_factors': {
                'item_level': item.item_level,
                'influences': item.influences,
                'special_properties': {
                    'synthesised': item.synthesised,
                    'fractured': item.fractured,
                    'six_linked': item._has_six_links()
                }
            }},
            last_updated=datetime.now()
        )

    def analyze_stash_value(self, items: List[StashItem]) -> StashValue:
        """
        Analyze total value of stash items

        Args:
            items: List of stash items to analyze

        Returns:
            StashValue with complete analysis
        """
        item_prices = []
        total_value = 0
        currency_value = 0
        equipment_value = 0
        other_value = 0
        category_breakdown = {}

        for item in items:
            try:
                price = self.price_item(item)
                item_prices.append(price)

                value = price.estimated_value
                total_value += value

                # Categorize value
                if item.category == "Currency":
                    currency_value += value
                elif item.category in ["Weapons", "Armour", "Accessories"]:
                    equipment_value += value
                else:
                    other_value += value

                # Category breakdown
                category = item.category
                category_breakdown[category] = category_breakdown.get(category, 0) + value

            except Exception as e:
                logger.error(f"Error pricing item {item.name}: {e}")
                continue

        # Sort items by value for top items
        item_prices.sort(key=lambda x: x.estimated_value, reverse=True)
        top_items = item_prices[:20]  # Top 20 most valuable

        # Count valuable items (worth more than 1 chaos)
        valuable_item_count = len([p for p in item_prices if p.estimated_value >= 1.0])

        return StashValue(
            total_value=total_value,
            currency_value=currency_value,
            equipment_value=equipment_value,
            other_value=other_value,
            item_count=len(items),
            valuable_item_count=valuable_item_count,
            top_items=top_items,
            category_breakdown=category_breakdown,
            analysis_timestamp=datetime.now()
        )

    def get_sell_recommendations(self, items: List[StashItem], min_value: float = 5.0) -> List[Dict]:
        """
        Get recommendations for items to sell

        Args:
            items: List of stash items
            min_value: Minimum value threshold for recommendations

        Returns:
            List of sell recommendations
        """
        recommendations = []

        for item in items:
            try:
                price = self.price_item(item)

                if price.estimated_value >= min_value:
                    recommendation = {
                        'item': item,
                        'price': price,
                        'sell_priority': self._calculate_sell_priority(item, price),
                        'recommendation_reason': self._get_recommendation_reason(item, price)
                    }
                    recommendations.append(recommendation)

            except Exception as e:
                logger.error(f"Error analyzing item for sell recommendation: {e}")
                continue

        # Sort by sell priority (higher is better)
        recommendations.sort(key=lambda x: x['sell_priority'], reverse=True)

        return recommendations

    def _calculate_sell_priority(self, item: StashItem, price: ItemPrice) -> float:
        """Calculate sell priority score for an item"""
        priority = price.estimated_value

        # Boost priority for high-confidence prices
        if price.confidence == "high":
            priority *= 1.5
        elif price.confidence == "medium":
            priority *= 1.2

        # Boost priority for currency (easy to sell)
        if item.category == "Currency":
            priority *= 1.3

        # Boost priority for items with player notes (already priced)
        if item.note:
            priority *= 1.4

        # Reduce priority for equipment (harder to sell)
        if item.category in ["Weapons", "Armour", "Accessories"] and item.rarity != "Unique":
            priority *= 0.8

        return priority

    def _get_recommendation_reason(self, item: StashItem, price: ItemPrice) -> str:
        """Get human-readable reason for sell recommendation"""
        reasons = []

        if price.estimated_value >= 50:
            reasons.append("High value item")
        elif price.estimated_value >= 10:
            reasons.append("Valuable item")

        if price.confidence == "high":
            reasons.append("Reliable market price")

        if item.category == "Currency":
            reasons.append("Easy to sell currency")

        if item.note:
            reasons.append("Already priced by player")

        if item.rarity == "Unique":
            reasons.append("Unique item with market demand")

        if not reasons:
            reasons.append("Above minimum value threshold")

        return ", ".join(reasons)

    def get_market_summary(self) -> Dict:
        """Get summary of current market data"""
        return {
            'league': self.league,
            'currency_count': len(self.currency_prices),
            'unique_count': len(self.unique_prices),
            'gem_count': len(self.gem_prices),
            'map_count': len(self.map_prices),
            'last_updated': datetime.now().isoformat(),
            'top_currencies': sorted(
                [(name, price) for name, price in self.currency_prices.items()],
                key=lambda x: x[1], reverse=True
            )[:10]
        }
