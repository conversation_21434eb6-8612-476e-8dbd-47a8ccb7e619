"""
Flask Web Application for POE Currency Analysis

A dedicated web interface for the Path of Exile currency price analysis tool.
Similar to poe.ninja but focused on personal analytics and trading insights.
"""

from flask import Flask, render_template, jsonify, request, send_from_directory, redirect
import os
import json
import pandas as pd
from datetime import datetime
from poe_get_currency_prices import POENinjaAPI, CurrencyDataManager
from poe_currency_analyzer import CurrencyAnalyzer
from poe_investment_calculator import InvestmentCalculator, Investment
from poe_account_integration import POEAccountAPI
from poe_stash_scanner import StashScanner
from poe_stash_pricer import StashPricer

app = Flask(__name__)
app.secret_key = 'poe-currency-analyzer-2025'

# Initialize components
data_manager = CurrencyDataManager()
analyzer = CurrencyAnalyzer(data_manager)
investment_calculator = InvestmentCalculator(data_manager)

# Initialize stash analysis components
# Note: You'll need to get these from POE developer portal
POE_CLIENT_ID = os.getenv('POE_CLIENT_ID', 'your_client_id_here')
POE_CLIENT_SECRET = os.getenv('POE_CLIENT_SECRET', 'your_client_secret_here')

poe_account_api = POEAccountAPI(POE_CLIENT_ID, POE_CLIENT_SECRET)
stash_scanner = StashScanner()
stash_pricer = StashPricer(league="Mercenaries")


@app.route('/')
def index():
    """Main dashboard page"""
    try:
        # Get latest snapshot for current prices
        latest_snapshot = data_manager.get_latest_snapshot()
        current_data = []
        last_updated = None
        
        if latest_snapshot and 'currency' in latest_snapshot['data']:
            df = data_manager.convert_to_dataframe(latest_snapshot['data']['currency'], 'currency')

            # Check if DataFrame has data and valid columns
            if not df.empty and 'chaos_equivalent' in df.columns:
                # Get top 20 currencies by value
                valid_currencies = df[df['chaos_equivalent'] > 0]
                if not valid_currencies.empty:
                    current_data = valid_currencies.nlargest(20, 'chaos_equivalent').to_dict('records')

            last_updated = latest_snapshot['timestamp']
        
        # Get historical analysis
        df_historical = analyzer.get_historical_data(days=7)
        analysis_summary = {}
        
        if not df_historical.empty:
            df_historical = analyzer.calculate_price_changes(df_historical)
            opportunities = analyzer.find_profit_opportunities(df_historical, min_change=5.0)
            top_gainers, top_losers = analyzer.get_top_movers(df_historical, n=5)
            
            analysis_summary = {
                'total_currencies': df_historical['name'].nunique(),
                'opportunities_count': len(opportunities),
                'top_gainers': top_gainers.to_dict('records') if not top_gainers.empty else [],
                'top_losers': top_losers.to_dict('records') if not top_losers.empty else [],
                'opportunities': opportunities.head(10).to_dict('records') if not opportunities.empty else []
            }
        
        return render_template('poe_dashboard.html', 
                             current_data=current_data,
                             analysis_summary=analysis_summary,
                             last_updated=last_updated)
                             
    except Exception as e:
        print(f"Error in dashboard route: {e}")
        return render_template('poe_dashboard.html', 
                             current_data=[],
                             analysis_summary={},
                             error=str(e))


@app.route('/api/current')
def api_current():
    """API endpoint for current currency data"""
    try:
        api = POENinjaAPI(league="Mercenaries")
        currency_data = api.get_currency_data('currency')
        
        if currency_data:
            return jsonify(currency_data)
        else:
            return jsonify({'error': 'Failed to fetch currency data'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/collect', methods=['POST'])
def api_collect():
    """API endpoint to trigger data collection"""
    try:
        api = POENinjaAPI(league="Mercenaries")
        
        # Fetch current data
        currency_data = api.get_currency_data('currency')
        fragment_data = api.get_currency_data('fragment')
        
        if currency_data:
            all_data = {'currency': currency_data}
            if fragment_data:
                all_data['fragment'] = fragment_data
                
            snapshot_path = data_manager.save_currency_snapshot(all_data)
            
            return jsonify({
                'success': True,
                'message': 'Data collected successfully',
                'snapshot_path': snapshot_path,
                'currency_count': len(currency_data.get('lines', [])),
                'fragment_count': len(fragment_data.get('lines', [])) if fragment_data else 0
            })
        else:
            return jsonify({'error': 'Failed to fetch currency data'}), 500
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/analysis')
def api_analysis():
    """API endpoint for currency analysis data"""
    try:
        days = request.args.get('days', 7, type=int)
        report = analyzer.generate_analysis_report(days=days)
        
        return jsonify(report)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/currency/<currency_name>')
def api_currency_detail(currency_name):
    """API endpoint for specific currency details"""
    try:
        df = analyzer.get_historical_data(days=30)
        
        if df.empty:
            return jsonify({'error': 'No historical data available'}), 404
            
        currency_data = df[df['name'] == currency_name].sort_values('timestamp')
        
        if currency_data.empty:
            return jsonify({'error': f'Currency {currency_name} not found'}), 404
            
        # Calculate additional metrics
        df_with_changes = analyzer.calculate_price_changes(df)
        currency_with_changes = df_with_changes[df_with_changes['name'] == currency_name].sort_values('timestamp')
        
        result = {
            'name': currency_name,
            'current_price': float(currency_data['chaos_equivalent'].iloc[-1]),
            'price_history': [
                {
                    'timestamp': row['timestamp'].isoformat(),
                    'price': float(row['chaos_equivalent']),
                    'change': float(row.get('price_change', 0)) if not pd.isna(row.get('price_change', 0)) else 0
                }
                for _, row in currency_with_changes.iterrows()
            ],
            'statistics': {
                'min_price': float(currency_data['chaos_equivalent'].min()),
                'max_price': float(currency_data['chaos_equivalent'].max()),
                'avg_price': float(currency_data['chaos_equivalent'].mean()),
                'volatility': float(currency_data['chaos_equivalent'].std())
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/charts')
def charts():
    """Charts page"""
    try:
        # Check if charts exist
        charts_dir = "poe_charts"
        dashboard_path = os.path.join(charts_dir, "dashboard.html")
        
        if os.path.exists(dashboard_path):
            # Read the dashboard HTML and serve it
            with open(dashboard_path, 'r', encoding='utf-8') as f:
                dashboard_content = f.read()
            return dashboard_content
        else:
            return render_template('poe_charts_placeholder.html')
            
    except Exception as e:
        return f"Error loading charts: {e}"


@app.route('/charts/<path:filename>')
def charts_files(filename):
    """Serve chart files"""
    try:
        return send_from_directory('poe_charts', filename)
    except Exception as e:
        return f"Error loading chart file: {e}", 404


@app.route('/data-collection')
def data_collection():
    """Data collection management page"""
    try:
        snapshots = data_manager.get_all_snapshots()
        
        snapshot_info = []
        for snapshot in snapshots:
            timestamp = snapshot['timestamp']
            currency_count = len(snapshot['data'].get('currency', {}).get('lines', []))
            fragment_count = len(snapshot['data'].get('fragment', {}).get('lines', []))
            
            snapshot_info.append({
                'timestamp': timestamp,
                'currency_count': currency_count,
                'fragment_count': fragment_count,
                'total_items': currency_count + fragment_count
            })
        
        return render_template('poe_data_collection.html', snapshots=snapshot_info)
        
    except Exception as e:
        return render_template('poe_data_collection.html', snapshots=[], error=str(e))


@app.route('/analysis')
def analysis():
    """Detailed analysis page"""
    try:
        days = request.args.get('days', 7, type=int)
        report = analyzer.generate_analysis_report(days=days)

        return render_template('poe_analysis.html', report=report, days=days)

    except Exception as e:
        return render_template('poe_analysis.html', report={'error': str(e)}, days=7)


@app.route('/investment')
def investment():
    """Investment calculator page"""
    try:
        # Get available currencies for dropdown
        latest_snapshot = data_manager.get_latest_snapshot()
        currencies = []

        if latest_snapshot and 'currency' in latest_snapshot['data']:
            df = data_manager.convert_to_dataframe(latest_snapshot['data']['currency'], 'currency')
            # Get currencies worth more than 1 chaos for investment
            valuable_currencies = df[df['chaos_equivalent'] >= 1.0].sort_values('chaos_equivalent', ascending=False)
            currencies = valuable_currencies[['name', 'chaos_equivalent']].to_dict('records')

        # Get portfolio performance
        portfolio_performance = investment_calculator.get_portfolio_performance()

        return render_template('poe_investment.html',
                             currencies=currencies,
                             portfolio=portfolio_performance)

    except Exception as e:
        return render_template('poe_investment.html',
                             currencies=[],
                             portfolio={'error': str(e)})


@app.route('/api/investment/scenario', methods=['POST'])
def api_investment_scenario():
    """API endpoint to calculate investment scenario"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        investment_amount = data.get('investment_amount')
        target_currency = data.get('target_currency')

        if not investment_amount or not target_currency:
            return jsonify({'error': 'Missing required fields'}), 400

        if investment_amount <= 0:
            return jsonify({'error': 'Investment amount must be positive'}), 400

        scenario = investment_calculator.calculate_investment_scenario(
            investment_amount=float(investment_amount),
            target_currency=target_currency
        )

        return jsonify(scenario)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/investment/create', methods=['POST'])
def api_create_investment():
    """API endpoint to create a new investment"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'error': 'No data provided'}), 400

        currency_name = data.get('currency_name')
        amount_invested = data.get('amount_invested')
        target_sell_price = data.get('target_sell_price')
        stop_loss_price = data.get('stop_loss_price')
        notes = data.get('notes', '')

        if not currency_name or not amount_invested:
            return jsonify({'error': 'Missing required fields'}), 400

        if amount_invested <= 0:
            return jsonify({'error': 'Investment amount must be positive'}), 400

        # Create investment
        investment = investment_calculator.create_investment(
            currency_name=currency_name,
            amount_invested=float(amount_invested),
            target_sell_price=float(target_sell_price) if target_sell_price else None,
            stop_loss_price=float(stop_loss_price) if stop_loss_price else None,
            notes=notes
        )

        # Save to portfolio
        success = investment_calculator.save_investment_to_portfolio(investment)

        if success:
            return jsonify({
                'success': True,
                'message': 'Investment created successfully',
                'investment': investment.to_dict()
            })
        else:
            return jsonify({'error': 'Failed to save investment'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/investment/portfolio')
def api_portfolio_performance():
    """API endpoint for portfolio performance"""
    try:
        performance = investment_calculator.get_portfolio_performance()
        return jsonify(performance)

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/investment/opportunities')
def api_investment_opportunities():
    """API endpoint for investment opportunities"""
    try:
        budget = request.args.get('budget', 100, type=float)
        min_return = request.args.get('min_return', 10.0, type=float)
        max_risk = request.args.get('max_risk', 'medium', type=str)

        opportunities = investment_calculator.find_investment_opportunities(
            budget=budget,
            min_expected_return=min_return,
            max_risk_level=max_risk
        )

        return jsonify({
            'opportunities': opportunities,
            'search_criteria': {
                'budget': budget,
                'min_return': min_return,
                'max_risk': max_risk
            }
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/stash')
def stash_analyzer():
    """Stash analyzer page"""
    try:
        # Check if user has linked POE account
        account = poe_account_api.load_account()

        if not account:
            # Show account linking page
            auth_url = poe_account_api.get_authorization_url()
            return render_template('poe_stash_link.html', auth_url=auth_url)

        # Show stash analyzer interface
        return render_template('poe_stash_analyzer.html', account=account)

    except Exception as e:
        return render_template('poe_stash_analyzer.html',
                             account=None,
                             error=str(e))


@app.route('/auth/callback')
def auth_callback():
    """OAuth callback for POE account linking"""
    try:
        code = request.args.get('code')
        error = request.args.get('error')

        if error:
            return render_template('poe_stash_link.html',
                                 error=f"Authentication failed: {error}")

        if not code:
            return render_template('poe_stash_link.html',
                                 error="No authorization code received")

        # Exchange code for token
        account = poe_account_api.exchange_code_for_token(code)

        if account:
            return redirect('/stash?linked=true')
        else:
            return render_template('poe_stash_link.html',
                                 error="Failed to link account")

    except Exception as e:
        return render_template('poe_stash_link.html',
                             error=f"Error linking account: {str(e)}")


@app.route('/api/stash/tabs')
def api_stash_tabs():
    """API endpoint to get stash tabs"""
    try:
        account = poe_account_api.load_account()

        if not account:
            return jsonify({'error': 'No linked POE account'}), 401

        league = request.args.get('league', 'Mercenaries')
        stash_tabs = poe_account_api.get_stash_tabs(account, league)

        if stash_tabs is None:
            return jsonify({'error': 'Failed to fetch stash tabs'}), 500

        # Simplify stash tab data for frontend
        simplified_tabs = []
        for tab in stash_tabs:
            simplified_tabs.append({
                'id': tab.get('id'),
                'name': tab.get('n', 'Unnamed'),
                'type': tab.get('type', 'NormalStash'),
                'index': tab.get('i', 0),
                'colour': tab.get('colour', {})
            })

        return jsonify({
            'stash_tabs': simplified_tabs,
            'account': account.account_name,
            'league': league
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/api/stash/analyze/<stash_id>')
def api_analyze_stash_tab(stash_id):
    """API endpoint to analyze a specific stash tab"""
    try:
        account = poe_account_api.load_account()

        if not account:
            return jsonify({'error': 'No linked POE account'}), 401

        league = request.args.get('league', 'Mercenaries')

        # Get stash tab contents
        stash_data = poe_account_api.get_stash_tab_contents(account, stash_id, league)

        if not stash_data:
            return jsonify({'error': 'Failed to fetch stash tab contents'}), 500

        # Scan items
        items = stash_scanner.scan_stash_tab(stash_data)

        # Analyze value
        stash_value = stash_pricer.analyze_stash_value(items)

        # Get sell recommendations
        recommendations = stash_pricer.get_sell_recommendations(items, min_value=1.0)

        # Get stash summary
        summary = stash_scanner.get_stash_summary(items)

        return jsonify({
            'stash_info': {
                'id': stash_id,
                'name': stash_data.get('stash', {}).get('n', 'Unknown'),
                'league': league
            },
            'value_analysis': {
                'total_value': stash_value.total_value,
                'currency_value': stash_value.currency_value,
                'equipment_value': stash_value.equipment_value,
                'other_value': stash_value.other_value,
                'item_count': stash_value.item_count,
                'valuable_item_count': stash_value.valuable_item_count,
                'category_breakdown': stash_value.category_breakdown
            },
            'top_items': [item.to_dict() for item in stash_value.top_items[:10]],
            'sell_recommendations': [
                {
                    'item_name': rec['item'].name,
                    'estimated_value': rec['price'].estimated_value,
                    'confidence': rec['price'].confidence,
                    'sell_priority': rec['sell_priority'],
                    'reason': rec['recommendation_reason']
                }
                for rec in recommendations[:10]
            ],
            'summary': summary
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@app.route('/favicon.ico')
def favicon():
    """Serve favicon to prevent 404 errors"""
    return '', 204  # No content


@app.errorhandler(404)
def not_found(error):
    return render_template('poe_404.html'), 404


@app.errorhandler(500)
def internal_error(error):
    return render_template('poe_500.html'), 500


if __name__ == '__main__':
    print("🎮 Starting POE Currency Analysis Web App")
    print("=" * 50)
    print("Access the application at: http://localhost:5006")
    print("Features:")
    print("  - Real-time currency prices")
    print("  - Historical price analysis")
    print("  - Trading opportunity detection")
    print("  - Interactive charts and visualizations")
    print("  - Data collection management")
    print()
    
    app.run(debug=True, port=5006, host='localhost')
